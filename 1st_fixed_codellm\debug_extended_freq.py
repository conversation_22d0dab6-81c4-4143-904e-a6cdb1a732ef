#!/usr/bin/env python3
"""
Debug script to understand the exact extended frequency calculation.
"""

import numpy as np
from scipy.fft import rfftfreq
from fborga_2d_3d import nextpow2, fftrl, gaussian_upou

# Test parameters
nt = 1024
dt = 0.002
t = np.arange(nt) * dt
signal = np.random.randn(nt)
fwidth = 10.0
finc = 2.0

# Get the frequency array like the original
n_padded = 2**nextpow2(nt)
spectrum, f = fftrl(signal, t, n_padded)

print(f"Original frequency array from fftrl:")
print(f"  Length: {len(f)}")
print(f"  Range: {f[0]} to {f[-1]} Hz")
print(f"  dx: {f[1] - f[0]}")

# Get the actual frequency grid from gaussian_upou
_, norm_factor, fnotvec_actual, nwin_actual, _ = gaussian_upou(f, f[0], fwidth, finc, gdb=np.inf)

print(f"\nActual gaussian_upou output:")
print(f"  nwin: {nwin_actual}")
print(f"  First 10: {fnotvec_actual[:10]}")
print(f"  Increment: {fnotvec_actual[1] - fnotvec_actual[0]}")

# Now let me manually replicate the exact logic
x = f
dx = x[1] - x[0]
xmin = x.min()
xmax = x.max()
nx = len(x)

print(f"\nManual replication:")
print(f"  Initial: nx={nx}, xmin={xmin}, xmax={xmax}, dx={dx}")

# Apply pow2option=1
nx_pow2 = 2**nextpow2(nx)
print(f"  nx after pow2: {nx_pow2}")

if nx_pow2 > len(x):
    x_padded = np.arange(nx_pow2) * dx + xmin
    xmax_extended = x_padded.max()
    print(f"  Extended: xmax_extended={xmax_extended}")
else:
    xmax_extended = xmax
    print(f"  No extension needed")

# Calculate nwin and frequency grid
nwin = round((xmax_extended - xmin) / finc) + 1
xinc_adjusted = (xmax_extended - xmin) / (nwin - 1)

print(f"  nwin: {nwin}")
print(f"  xinc_adjusted: {xinc_adjusted}")

# Generate frequency grid like gaussian_upou
x0 = xmin
xnotvec_manual = np.zeros(nwin)
for k in range(nwin):
    xnotvec_manual[k] = x0
    x0 += xinc_adjusted
    # This is the key line - quantization to the original grid
    x0 = dx * round((x0 - xmin) / dx) + xmin
    if k + 1 == nwin and x0 > xmax_extended:
        x0 = xmax_extended

print(f"  Manual first 10: {xnotvec_manual[:10]}")
print(f"  Manual increment: {xnotvec_manual[1] - xnotvec_manual[0]}")

print(f"\nComparison:")
print(f"  Actual vs manual first 10 match: {np.allclose(fnotvec_actual[:10], xnotvec_manual[:10])}")
print(f"  Differences in first 10: {fnotvec_actual[:10] - xnotvec_manual[:10]}")

# Test my optimized implementation
freqs = f
dx_opt = float(freqs[1] - freqs[0])
xmin_opt = float(freqs[0])
xmax_opt = float(freqs[-1])
nx_opt = len(freqs)

nx_extended_opt = 2**int(np.ceil(np.log2(nx_opt)))
if nx_extended_opt > nx_opt:
    xmax_extended_opt = xmin_opt + (nx_extended_opt - 1) * dx_opt
else:
    xmax_extended_opt = xmax_opt

nwin_opt = round((xmax_extended_opt - xmin_opt) / finc) + 1
centres_opt = np.linspace(xmin_opt, xmax_extended_opt, nwin_opt)

print(f"\nMy optimized implementation:")
print(f"  nx_extended: {nx_extended_opt}")
print(f"  xmax_extended: {xmax_extended_opt}")
print(f"  nwin: {nwin_opt}")
print(f"  First 10: {centres_opt[:10]}")
print(f"  Increment: {centres_opt[1] - centres_opt[0]}")

print(f"\nComparison with actual:")
print(f"  My vs actual first 10 match: {np.allclose(fnotvec_actual[:10], centres_opt[:10])}")
print(f"  Differences in first 10: {fnotvec_actual[:10] - centres_opt[:10]}")