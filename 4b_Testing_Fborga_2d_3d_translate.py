# -*- coding: utf-8 -*-
"""
fborga_extended.py

Original script by dev<PERSON><PERSON>agustian<PERSON>, based on <PERSON><PERSON><PERSON><PERSON>'s Matlab code.
This version is extended by an AI assistant to handle 2D and 3D seismic data,
and to allow for the selection of specific output frequencies.
CORRECTED VERSION: Fixes the ImportError for 'ifftrl'.
"""

import numpy as np
from scipy import signal as sig
# CORRECTED IMPORT: Import the base functions rfft, irfft, rfftfreq
from scipy.fft import rfft, irfft, rfftfreq
from tqdm import tqdm
import matplotlib.pyplot as plt

# --- Start of Helper and Original fborga.py Code (with fixes) ---

# Global variable initialization
XNOM = None
GAUS = None
DX = None
XMIN = None
XMAX = None
XWID = None

def nextpow2(x):
    return int(np.ceil(np.log2(np.abs(x))))

# RESTORED HELPER FUNCTION: This function was in the original script
def fftrl(signal, t, pad_to=0):
    dt = t[1] - t[0]
    nt = len(signal)
    if pad_to == 0:
        pad_to = nt
    if pad_to < nt:
        pad_to = nt
    # nf = pad_to // 2 + 1 # This is implicitly handled by rfft
    f = rfftfreq(pad_to, dt)
    spectrum = rfft(signal, n=pad_to)
    return spectrum, f

# RESTORED HELPER FUNCTION: This function was in the original script
def ifftrl(spectrum, f):
    nf = len(f)
    # For real-valued signals, the original length corresponding
    # to nf frequencies is nt = 2 * (nf - 1)
    nt = 2 * (nf - 1)
    signal = irfft(spectrum, n=nt)
    return signal

def gaussian_upou(x, xnot, xwid, xinc, norm_factor=None, xnotvec=None, gdb=60, pow2option=1):
    global XNOM, GAUS, DX, XMIN, XMAX, XWID
    x = np.atleast_1d(x).flatten()
    dx = x[1] - x[0]
    xmin = x.min()
    xmax = x.max()
    nx = len(x)
    if pow2option:
        nx = 2**nextpow2(nx)
    if nx > len(x):
        x_padded = np.arange(nx) * dx + xmin
        xmax = x_padded.max()
        x = x_padded
    makeit = False
    if GAUS is None or dx != DX or xmin != XMIN or xmax != XMAX or xwid != XWID or nx != len(GAUS):
        makeit = True
    if makeit:
        DX = dx; XMIN = xmin; XMAX = xmax; XWID = xwid
        XNOM = np.arange(xmin-xmax, xmax-xmin+dx, dx)
        GAUS = np.exp(-(XNOM/xwid)**2)
    nwin = round((xmax - xmin) / xinc) + 1
    xinc = (xmax - xmin) / (nwin - 1)
    Xdb = xwid * np.sqrt(gdb / (20 * np.log10(np.e)))
    if np.isinf(Xdb): Xdb = (xmax - xmin) / 2
    nXdb = round(Xdb / dx)
    nwinstandard = 2 * nXdb
    if pow2option:
        nwinstandard = 2**nextpow2(nwinstandard)
        nXdb = nwinstandard // 2
    if nwinstandard > nx: nwinstandard = nx
    if nwinstandard == nx - 1: nwinstandard = nx
    if norm_factor is None or len(norm_factor) != nx or np.sum(np.abs(norm_factor)) == 0:
        norm_factor = np.zeros_like(x)
        x0 = xmin
        xnotvec = np.zeros(nwin)
        for k in range(nwin):
            xnotvec[k] = x0
            gwinnom = get_gaussian(x, x0)
            igoff = np.abs((x - x0) / dx)
            iuse2 = np.where(igoff <= nXdb)[0]
            if len(iuse2) == 0: continue
            iuse = make_standard_size(iuse2, nwinstandard, nx)
            gwin = gwinnom[iuse]
            norm_factor[iuse] += gwin**2
            x0 += xinc
            x0 = dx * round((x0 - xmin) / dx) + xmin
            if k + 1 == nwin and x0 > xmax: x0 = xmax
    ind = np.argmin(np.abs(xnotvec - xnot))
    xnot = xnotvec[ind]
    gwinnom = get_gaussian(x, xnot)
    igoff = np.abs((x - xnot) / dx)
    iuse2 = np.where(igoff <= nXdb)[0]
    if len(iuse2) == 0: return np.zeros(nwinstandard), norm_factor, xnotvec, nwin, 0
    iuse = make_standard_size(iuse2, nwinstandard, nx)
    gwin = gwinnom[iuse]
    norm_values = norm_factor[iuse]
    non_zero_norm = norm_values != 0
    gwin[non_zero_norm] = gwin[non_zero_norm] / norm_values[non_zero_norm]
    return gwin, norm_factor, xnotvec, nwin, iuse[0]

def make_standard_size(iuse2, nwinstandard, nx):
    nu = len(iuse2)
    if nu < nwinstandard:
        if iuse2[0] == 0: iuse = np.arange(nwinstandard)
        elif iuse2[-1] == nx - 1: iuse = np.arange(nx-nwinstandard, nx)
        else:
            needed = nwinstandard - nu
            pad_before = needed // 2
            start = iuse2[0] - pad_before
            iuse = np.arange(start, start + nwinstandard)
    else: iuse = iuse2[:nwinstandard]
    iuse = np.clip(iuse, 0, nx-1)
    return iuse.astype(int)

def get_gaussian(x, x0):
    global GAUS, XMAX, XMIN, DX
    xnom1 = x[0] - x0
    inom1 = int(round((xnom1 + XMAX - XMIN) / DX))
    inom = np.arange(inom1, inom1 + len(x)).astype(int)
    inom = np.clip(inom, 0, len(GAUS)-1)
    return GAUS[inom]

def fborga(signal, t, fwidth, finc, padflag=1):
    signal = np.nan_to_num(signal)
    nt = len(signal)
    original_length = nt
    n_padded = 2**nextpow2(nt) if padflag else nt
    spectrum, f = fftrl(signal, t, n_padded)
    fmin = f[0]
    _, norm_factor, fnotvec, nwin, _ = gaussian_upou(f, fmin, fwidth, finc, gdb=np.inf)
    tvs = np.zeros((original_length, nwin), dtype=float)
    for k in range(nwin):
        fnow = fnotvec[k]
        g, _, _, _, i1 = gaussian_upou(f, fnow, fwidth, finc, norm_factor, fnotvec, gdb=np.inf)
        win = np.zeros_like(f, dtype=float)
        end_idx = i1 + len(g)
        if end_idx > len(win):
            win[i1:] = g[:len(win)-i1]
        else:
            win[i1:end_idx] = g
        S = spectrum * win
        ifft_result = ifftrl(S, f)
        tvs[:, k] = ifft_result[:original_length]
    return tvs, fnotvec, t[:original_length]

# --- NEW FUNCTIONS FOR 2D and 3D DATA with FREQUENCY SELECTION ---

def fborga_2d(seismic_section, t, fwidth, finc, padflag=1, target_freqs=None):
    if seismic_section.ndim != 2:
        raise ValueError("Input seismic_section must be a 2D array.")
    n_samples, n_traces = seismic_section.shape
    print(f"Processing 2D section with {n_traces} traces...")
    first_trace = seismic_section[:, 0]
    tvs_full, fout_full, t_out = fborga(first_trace, t, fwidth, finc, padflag)
    if target_freqs is not None:
        target_freqs = np.atleast_1d(target_freqs)
        freq_indices = np.array([np.argmin(np.abs(fout_full - f_target)) for f_target in target_freqs])
        fout_selected = fout_full[freq_indices]
        tvs_selected = tvs_full[:, freq_indices]
        print(f"Selecting {len(fout_selected)} frequencies closest to targets: {target_freqs}")
    else:
        freq_indices = slice(None)
        fout_selected = fout_full
        tvs_selected = tvs_full
        print(f"No target frequencies specified, generating all {len(fout_selected)} slices.")
    n_freqs_out = len(fout_selected)
    tvs_3d = np.zeros((n_samples, n_freqs_out, n_traces))
    tvs_3d[:, :, 0] = tvs_selected
    for i in tqdm(range(1, n_traces), desc="Applying Borga Transform (2D)"):
        trace = seismic_section[:, i]
        tvs_i, _, _ = fborga(trace, t, fwidth, finc, padflag)
        tvs_3d[:, :, i] = tvs_i[:, freq_indices]
    print("2D Borga transform complete.")
    return tvs_3d, fout_selected, t_out

def fborga_3d(seismic_volume, t, fwidth, finc, padflag=1, target_freqs=None):
    if seismic_volume.ndim != 3:
        raise ValueError("Input seismic_volume must be a 3D array.")
    n_samples, n_inlines, n_xlines = seismic_volume.shape
    print(f"Processing 3D volume with {n_inlines} inlines and {n_xlines} xlines...")
    first_trace = seismic_volume[:, 0, 0]
    tvs_full, fout_full, t_out = fborga(first_trace, t, fwidth, finc, padflag)
    if target_freqs is not None:
        target_freqs = np.atleast_1d(target_freqs)
        freq_indices = np.array([np.argmin(np.abs(fout_full - f_target)) for f_target in target_freqs])
        fout_selected = fout_full[freq_indices]
        tvs_selected = tvs_full[:, freq_indices]
        print(f"Selecting {len(fout_selected)} frequencies closest to targets: {target_freqs}")
    else:
        freq_indices = slice(None)
        fout_selected = fout_full
        tvs_selected = tvs_full
        print(f"No target frequencies specified, generating all {len(fout_selected)} slices.")
    n_freqs_out = len(fout_selected)
    tvs_4d = np.zeros((n_samples, n_freqs_out, n_inlines, n_xlines))
    tvs_4d[:, :, 0, 0] = tvs_selected
    for il in tqdm(range(n_inlines), desc="Processing Inlines"):
        for xl in range(n_xlines):
            if il == 0 and xl == 0: continue
            trace = seismic_volume[:, il, xl]
            tvs_i, _, _ = fborga(trace, t, fwidth, finc, padflag)
            tvs_4d[:, :, il, xl] = tvs_i[:, freq_indices]
    print("3D Borga transform complete.")
    return tvs_4d, fout_selected, t_out

# --- DEMONSTRATION ---
if __name__ == '__main__':
    nt = 512
    dt = 0.002
    t = np.arange(0, nt * dt, dt)
    fwidth = 10.0
    finc = 2.0
    n_traces_2d = 50
    seismic_2d = np.zeros((nt, n_traces_2d))
    for i in range(n_traces_2d):
        trace = np.sin(2 * np.pi * 20 * t) * sig.windows.tukey(nt, 0.25)
        trace += 1.2 * np.sin(2 * np.pi * 55 * t) * sig.windows.tukey(nt, 0.25, nt // 2)
        trace += np.random.randn(nt) * 0.2
        seismic_2d[:, i] = trace
    
    target_frequencies = [20, 55, 80]
    tvs_3d_selected, fout_selected, t_out = fborga_2d(
        seismic_2d, t, fwidth, finc, target_freqs=target_frequencies
    )
    
    print("\n--- 2D Analysis with Frequency Selection ---")
    print(f"Input 2D section shape: {seismic_2d.shape}")
    print(f"Requested frequencies: {target_frequencies} Hz")
    print(f"Actual frequencies returned: {fout_selected} Hz")
    print(f"Output 3D Borga spectrum shape: {tvs_3d_selected.shape}")

    fig, axes = plt.subplots(1, 3, figsize=(15, 6), sharey=True)
    fig.suptitle('Borga Frequency Slices from Targeted Selection', fontsize=16)

    for i in range(len(fout_selected)):
        ax = axes[i]
        freq_slice = tvs_3d_selected[:, i, :]
        im = ax.imshow(freq_slice, aspect='auto', cmap='seismic',
                       extent=[0, n_traces_2d, t_out[-1], t_out[0]],
                       vmin=-np.max(np.abs(freq_slice)), vmax=np.max(np.abs(freq_slice)))
        ax.set_title(f'Slice at {fout_selected[i]:.1f} Hz')
        ax.set_xlabel('Trace Number')
        fig.colorbar(im, ax=ax, orientation='horizontal', pad=0.15)
    
    axes[0].set_ylabel('Time (s)')
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.show()