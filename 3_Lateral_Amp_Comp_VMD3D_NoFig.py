# -*- coding: utf-8 -*-
"""
Created on Sun Jul 21 03:11:40 2024

@author: mutia
"""

# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]

import numpy as np
import matplotlib.pyplot as plt
import segyio
from scipy.signal import hilbert
from scipy.ndimage import uniform_filter1d
from sklearn.utils import resample
from libvmd.vmd import vmd2
import time

def read_3d_segy(file_path, inline_range):
    with segyio.open(file_path, "r") as segy:
        # Get inline numbers
        inlines = segy.attributes(segyio.TraceField.INLINE_3D)[:]
        
        # Find traces for the specified inline range
        start_inline, end_inline = inline_range
        trace_mask = (inlines >= start_inline) & (inlines <= end_inline)
        
        # Read data for the specified inline range
        data = np.array([segy.trace.raw[i] for i in range(len(inlines)) if trace_mask[i]])
        segy_trace_headers = [dict(segy.header[i]) for i in range(len(inlines)) if trace_mask[i]]
        segy_header = segy.text[0]
    return data, segy_trace_headers, segy_header

def env(x):
    return np.abs(hilbert(x))

def format_time(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{seconds:05.2f}"

# Read 3D SEG-Y file for specified inline range
# file_path = r"D:\OneDrive - PT Pertamina (Persero)\12_Matlab_PKB\PKB\STRANSBAL\SEGY_Test\2014_OBC_PSDM_BEAM_FULL_AZ1_FPP_ILAOI_TIME.sgy"
file_path = r"C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\12_Matlab_PKB\PKB\Strans_Balans\SEGY_Test\2014_OBC_PSDM_BEAM_FULL_AZ1_FPP_ILAOI_TIME.sgy"
inline_range = (12740, 12750)
Data, SegyTraceHeaders, SegyHeader = read_3d_segy(file_path, inline_range)

# Process time data
dt = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_INTERVAL] / 1000000
num_samples = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_COUNT]
tseis = np.arange(num_samples) * dt * 1000 - dt

seis_avoa = np.transpose(Data)
cdp_seis = seis_avoa.shape[1]
cdp_num = np.arange(1, cdp_seis + 1)

# 2D VMD parameters
alpha = 1000
tau = 0.01
K = 3
DC = True
init = 1
tol = 1e-6
N = 500

twt_ms = tseis

# Start timing
start_time = time.time()

print("Starting 2D VMD process...")

# 2D process find operator by random resampling
rat = 0.5
pwr_env = 1
sm = 400
ncomp = K

# Resample the data
bnm = int(cdp_seis * rat)
seis_samp = resample(seis_avoa.T, n_samples=bnm, random_state=42).T

# Perform 2D VMD on the resampled data
u_samp, _, _ = vmd2(seis_samp, K, alpha=alpha, tau=tau, DC=DC, init=init, tol=tol, N=N)

# Calculate envelope
env_trc = np.zeros((seis_samp.shape[0], seis_samp.shape[1], ncomp))
for k in range(ncomp):
    env_trc[:, :, k] = env(u_samp[:, :, k]) ** pwr_env

# Calculate average envelope
avg_env = np.zeros((seis_samp.shape[0], ncomp))
for k in range(ncomp):
    out_sum = np.mean(env_trc[:, :, k], axis=1) + 0.01  # teta = 0.01
    avg_env[:, k] = uniform_filter1d(out_sum, size=sm)

# Perform 2D VMD on the entire seismic data
u, _, _ = vmd2(seis_avoa, K, alpha=alpha, tau=tau, DC=DC, init=init, tol=tol, N=N)

# 2D VMD normalization
seis_bal = np.zeros_like(seis_avoa)
for k in range(ncomp):
    env_trc = uniform_filter1d(env(u[:, :, k]) ** pwr_env, size=sm, axis=0)
    seis_bal += avg_env[:, k][:, np.newaxis] * (u[:, :, k] / (env_trc * (1 + 0.01)))  # teta = 0.01

# End timing
end_time = time.time()
total_time = end_time - start_time

print(f"2D VMD process and normalization completed.")
print(f"Total processing time: {format_time(total_time)}")

Data_bal = seis_bal[:len(tseis), :]
Data_bal[np.isnan(Data_bal)] = 0

# After processing, write the balanced data to a new SEG-Y file
output_file = "output_balanced_inlines_12740-12750.sgy"

# Calculate the number of inlines and crosslines
n_inlines = inline_range[1] - inline_range[0] + 1
n_xlines = len(SegyTraceHeaders) // n_inlines

# Prepare the spec object for creating the SEG-Y file
spec = segyio.spec()
spec.sorting = 1  # INLINE_SORTING
spec.format = 1  # IBM float
spec.samples = tseis
spec.ilines = range(inline_range[0], inline_range[1] + 1)
spec.xlines = range(n_xlines)

# Create and populate the SEG-Y file
with segyio.create(output_file, spec) as f:
    # Write trace data
    f.trace = Data_bal.T
    
    # Copy headers from input to output
    for i, header in enumerate(SegyTraceHeaders):
        f.header[i] = header
    
    # Update inline and crossline numbers in headers
    for i, inline in enumerate(range(inline_range[0], inline_range[1] + 1)):
        for j in range(n_xlines):
            trace_index = i * n_xlines + j
            f.header[trace_index][segyio.TraceField.INLINE_3D] = inline
            f.header[trace_index][segyio.TraceField.CROSSLINE_3D] = j + 1  # Assuming crosslines start from 1
    
    # Copy text headers
    f.text[0] = SegyHeader

print(f"Output SEG-Y file saved as: {output_file}")