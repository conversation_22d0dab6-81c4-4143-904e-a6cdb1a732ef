# -*- coding: utf-8 -*-
"""
Created on Sun Jul  7 03:02:45 2024

@author: devri.agustianto

% FBORGA performs a forward Borga transform of a seismic trace using
% modified Gaussian analysis windows. The Borga transform is implemented by
% a forward Fourier transform, frequency slicing using modified Gaussian
% windows, and then inverse Fourier transfoming each slice. The output is a
% 2D matrix, called tvs, with the row coordinate being time and the column
% coordinate being the center frequency of each frequency slice. This tvs,
% or time variant spectrum, is also called the Borga spectrum. The Borga
% spectrum is a time-frequency decomposition that is the formal adjoint of
% the Gabor transform (hint: the name Borga is a joke). While the Gabor
% transform is complex valued, the Borga transform is real valued.
% Essentially each column (trace) of the Borga transform is a filter slice
% of the input signal. The Borga spectrum may be inverted s=sum(tvs,2);
% where s will be the reconstructed trace.
%
% signal= input trace 
% t= time coordinate vector for signal.
% fwidth= width (Hertz) of the Gaussian window.
% finc= shift (Hertz) between windows.
% Note: Generally finc < fwidth.
% padflag= if 0, the trace is transformed without padding. If 1,
%   it is padded with zeros to the next power of 2 (unless it already is
%   a power of 2)
% ************** default padflag = 1 ***************
% tvs= output time-variant spectrum (complex valued). Each column is a time
%       series for a particular frequency.
% fout= row vector giving the column coordinate of tvs
% NOTE: the column vector giving the row coordinate of tvs is the same as
%   the input t
%
% by G.F. Margrave, July 2009-2014
%
% NOTE: This SOFTWARE may be used by any individual or corporation for any purpose
% with the exception of re-selling or re-distributing the SOFTWARE.
% By using this software, you are agreeing to the terms detailed in this software's
% Matlab source file.

% BEGIN TERMS OF USE LICENSE
%
% This SOFTWARE is maintained by the CREWES Project at the Department
% of Geology and Geophysics of the University of Calgary, Calgary,
% Alberta, Canada.  The copyright and ownership is jointly held by
% its 'AUTHOR' (identified above) and the CREWES Project.  The CREWES
% project may be contacted via email at:  <EMAIL>
%
% The term 'SOFTWARE' refers to the Matlab source code, translations to
% any other computer language, or object code
%
% Terms of use of this SOFTWARE
%
% 1) This SOFTWARE may be used by any individual or corporation for any purpose
%    with the exception of re-selling or re-distributing the SOFTWARE.
%
% 2) The AUTHOR and CREWES must be acknowledged in any resulting publications or
%    presentations
%
% 3) This SOFTWARE is provided "as is" with no warranty of any kind
%    either expressed or implied. CREWES makes no warranties or representation
%    as to its accuracy, completeness, or fitness for any purpose. CREWES
%    is under no obligation to provide support of any kind for this SOFTWARE.
%
% 4) CREWES periodically adds, changes, improves or updates this SOFTWARE without
%    notice. New versions will be made available at www.crewes.org .
%
% 5) Use this SOFTWARE at your own risk.
%
% END TERMS OF USE LICENSE
"""

import numpy as np
from scipy import signal as sig
from scipy.fft import fft, ifft, rfft, irfft, rfftfreq

# Global variable initialization
XNOM = None
GAUS = None
DX = None
XMIN = None
XMAX = None
XWID = None

def nextpow2(x):
    return int(np.ceil(np.log2(np.abs(x))))

def fftrl(signal, t, pad_to=0):
    dt = t[1] - t[0]
    nt = len(signal)
    if pad_to == 0:
        pad_to = nt
    nf = pad_to // 2 + 1
    f = rfftfreq(pad_to, dt)
    spectrum = rfft(signal, n=pad_to)
    return spectrum, f

def ifftrl(spectrum, f):
    nf = len(f)
    nt = 2 * (nf - 1)
    signal = irfft(spectrum, n=nt)
    return signal

def gaussian_upou(x, xnot, xwid, xinc, norm_factor=None, xnotvec=None, gdb=60, pow2option=1):
    global XNOM, GAUS, DX, XMIN, XMAX, XWID
    
    x = np.atleast_1d(x).flatten()
    
    dx = x[1] - x[0]
    xmin = x.min()
    xmax = x.max()
    nx = len(x)
    if pow2option:
        nx = 2**nextpow2(nx)
    if nx > len(x):
        x = np.arange(nx) * dx + xmin
        xmax = x.max()
    
    makeit = False
    if GAUS is None:
        makeit = True
    elif dx != DX or xmin != XMIN or xmax != XMAX or xwid != XWID or nx != len(GAUS):
        makeit = True
    
    if makeit:
        DX = dx
        XMIN = xmin
        XMAX = xmax
        XWID = xwid
        XNOM = np.arange(xmin-xmax, xmax-xmin+dx, dx)
        GAUS = np.exp(-(XNOM/xwid)**2)
    
    nwin = round((xmax - xmin) / xinc) + 1
    xinc = (xmax - xmin) / (nwin - 1)
    
    Xdb = xwid * np.sqrt(gdb / (20 * np.log10(np.e)))
    if np.isinf(Xdb):
        Xdb = (xmax - xmin) / 2
    nXdb = round(Xdb / dx)
    nwinstandard = 2 * nXdb
    
    if pow2option:
        nwinstandard = 2**nextpow2(nwinstandard)
        nXdb = nwinstandard // 2
        Xdb = dx * nXdb
    
    if nwinstandard > nx:
        nwinstandard = nx
        nXdb = nwinstandard // 2
        Xdb = dx * nXdb
    
    if nwinstandard == nx - 1:
        nwinstandard = nx
    
    if norm_factor is None or len(norm_factor) != nx or np.sum(np.abs(norm_factor)) == 0:
        norm_factor = np.zeros_like(x)
        x0 = xmin
        xnotvec = np.zeros(nwin)
        for k in range(nwin):
            xnotvec[k] = x0
            gwinnom = get_gaussian(x, x0)
            igoff = np.abs((x - x0) / dx)
            iuse2 = np.where(igoff <= nXdb)[0]
            iuse = make_standard_size(iuse2, nwinstandard, nx)
            gwin = gwinnom[iuse]
            norm_factor[iuse] += gwin
            x0 = x0 + xinc
            x0 = dx * round((x0 - xmin) / dx) + xmin
            if k + 1 == nwin and x0 > xmax:
                x0 = xmax
    
    ind = np.argmin(np.abs(xnotvec - xnot))
    xnot = xnotvec[ind]
    
    gwinnom = get_gaussian(x, xnot)
    igoff = np.abs((x - xnot) / dx)
    iuse2 = np.where(igoff <= nXdb)[0]
    iuse = make_standard_size(iuse2, nwinstandard, nx)
    gwin = gwinnom[iuse]
    gwin = gwin / norm_factor[iuse]
    i1 = iuse[0]
    
    return gwin, norm_factor, xnotvec, nwin, i1

def make_standard_size(iuse2, nwinstandard, nx):
    if len(iuse2) < nwinstandard:
        if iuse2[0] == 0:
            nu = len(iuse2)
            iuse = np.concatenate((iuse2, np.arange(iuse2[-1]+1, iuse2[-1]+nwinstandard-nu+1)))
        elif iuse2[-1] == nx - 1:
            nu = len(iuse2)
            iuse = np.concatenate((np.arange(iuse2[0]-nwinstandard+nu, iuse2[0]), iuse2))
        else:
            raise ValueError('Total logic failure in gaussian_upou')
    elif len(iuse2) == nwinstandard + 1:
        iuse = iuse2[:-1]
    else:
        iuse = iuse2
    
    if len(iuse) != nwinstandard:
        raise ValueError('Failed to create standard size window')
    
    return iuse

def get_gaussian(x, x0):
    global GAUS, XMAX, XMIN, DX
    xnom1 = x[0] - x0
    inom1 = round((xnom1 + XMAX - XMIN) / DX)
    inom = np.arange(inom1, inom1 + len(x))
    return GAUS[inom]

def fborga(signal, t, fwidth, finc, padflag=1):
    nt = len(signal)
    original_length = nt  # Store the original length
    if padflag:
        nt = 2**nextpow2(nt)
    
    spectrum, f = fftrl(signal, t, nt)
    fmin = f[0]
    
    ls = len(spectrum)
    iuse = np.arange(ls)
    if ls % 2 != 0:
        spectrum = np.pad(spectrum, (0, 1), mode='constant')
        f = np.pad(f, (0, 1), mode='constant', constant_values=(f[-1] + f[1] - f[0],))
        iuse = np.arange(ls + 1)
    
    gdb = np.inf
    g, norm_factor, fnotvec, nwin, _ = gaussian_upou(f, fmin, fwidth, finc, None, None, gdb, 1)
    
    # Initialize tvs with the correct size
    tvs = np.zeros((original_length, nwin))
    fout = np.zeros(nwin)
    
    for k in range(nwin):
        fnow = fnotvec[k]
        fout[k] = fnow
        g, _, _, _, _ = gaussian_upou(f, fnow, fwidth, finc, norm_factor, fnotvec, gdb, 1)
        
        # Ensure g has the same length as spectrum
        if len(g) > len(spectrum):
            g = g[:len(spectrum)]
        elif len(g) < len(spectrum):
            g = np.pad(g, (0, len(spectrum) - len(g)), mode='constant')
        
        S = spectrum[iuse] * g
        ifft_result = ifftrl(S, f[iuse])
        
        # Scale or stretch/squeeze ifft_result to match original_length
        if len(ifft_result) != original_length:
            tvs[:, k] = sig.resample(ifft_result, original_length)
        else:
            tvs[:, k] = ifft_result
    
    return tvs, fout, t

def fborga_win(signal, t, fwidth, finc, padflag=1):
    nt = len(signal)
    original_length = nt  # Store the original length
    if padflag:
        nt = 2**nextpow2(nt)
    
    spectrum, f = fftrl(signal, t, nt)
    fmin = f[0]
    
    ls = len(spectrum)
    iuse = np.arange(ls)
    if ls % 2 != 0:
        spectrum = np.pad(spectrum, (0, 1), mode='constant')
        f = np.pad(f, (0, 1), mode='constant', constant_values=(f[-1] + f[1] - f[0],))
        iuse = np.arange(ls + 1)
    
    gdb = np.inf
    g, norm_factor, fnotvec, nwin, _ = gaussian_upou(f, fmin, fwidth, finc, None, None, gdb, 1)
    
    # Initialize tvs with the correct size
    tvs = np.zeros((original_length, nwin), dtype=complex)
    fout = np.zeros(nwin)
    gaussian_windows = []  # To store Gaussian windows
    time_domain_windows = []  # To store time-domain Gaussian windows
    time_coordinates = []  # To store time coordinates for time-domain windows
    
    # Calculate the time step for the padded signal
    dt = t[1] - t[0]
    
    for k in range(nwin):
        fnow = fnotvec[k]
        fout[k] = fnow
        g, _, _, _, _ = gaussian_upou(f, fnow, fwidth, finc, norm_factor, fnotvec, gdb, 1)
        
        gaussian_windows.append(g)
        
        if len(g) > len(spectrum):
            g = g[:len(spectrum)]
        elif len(g) < len(spectrum):
            g = np.pad(g, (0, len(spectrum) - len(g)), mode='constant')
        
        S = spectrum[iuse] * g
        ifft_result = ifftrl(S, f[iuse])
        
        # Convert frequency domain Gaussian to time domain
        time_domain_g = ifftrl(g, f[iuse])
        
        # Circular shift to center the window
        shift = len(time_domain_g) // 2
        time_domain_g = np.roll(time_domain_g, shift)

        # Create centered time coordinate for the full length
        t_centered = np.arange(-len(time_domain_g)//2, len(time_domain_g)//2) * dt
        time_coordinates.append(t_centered)
        
        # Pad time_domain_g if it's shorter than t_centered
        if len(time_domain_g) < len(t_centered):
            pad_width = (len(t_centered) - len(time_domain_g)) // 2
            time_domain_g = np.pad(time_domain_g, (pad_width, pad_width), mode='constant')
        
        # Trim time_domain_g if it's longer than t_centered
        elif len(time_domain_g) > len(t_centered):
            trim = (len(time_domain_g) - len(t_centered)) // 2
            time_domain_g = time_domain_g[trim:-trim]
        
        time_domain_windows.append(time_domain_g)
        
        # Store the result in tvs
        if len(ifft_result) != original_length:
            tvs[:, k] = sig.resample(ifft_result, original_length)
        else:
            tvs[:, k] = ifft_result[:original_length]
    
    # Ensure t_out matches the signal length
    t_out = t[:original_length]
    
    return tvs, fout, t_out, gaussian_windows, time_domain_windows, time_coordinates