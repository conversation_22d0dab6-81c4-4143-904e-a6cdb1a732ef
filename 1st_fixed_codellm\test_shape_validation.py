#!/usr/bin/env python3
"""
Test script to validate that the optimized implementation produces 
the same output shape as the original implementation.
"""

import numpy as np
import time

from fborga_2d_3d import fborga_2d as fborga_2d_orig  # Original
from fborga_2d_3d_optimized_fixed import fborga_2d as fborga_2d_opt

# --- Synthetic Data for Testing ---
nt, ntr = 1024, 50
f1, f2 = 15, 55  # Hz
np.random.seed(42)
dt = 0.002  # 2 ms

t = np.arange(nt) * dt
section = (
    np.sin(2 * np.pi * f1 * t)[:, None] * np.ones((1, ntr))
    + 0.7 * np.sin(2 * np.pi * f2 * t)[:, None] * np.ones((1, ntr))
    + 0.1 * np.random.randn(nt, ntr)
)

# --- Parameters ---
fwidth = 10.0
finc = 2.0
target_freqs = [5, 10, 15, 20, 25, 30]  # Hz

print("\n== Shape Validation Test ==")
print(f"Section shape: {section.shape}, dt: {dt}")
print(f"Target frequencies: {target_freqs} Hz, fwidth: {fwidth}, finc: {finc}\n")

# --- Run Original Implementation (NumPy/CPU) ---
print("Running original implementation...")
start = time.time()
tvs_ref, fout_ref, t_out_ref = fborga_2d_orig(
    section, t, fwidth, finc, target_freqs=target_freqs
)
elapsed_ref = time.time() - start
print(f"Original fborga_2d time: {elapsed_ref:.3f} s")
print(f"Original output shape: {tvs_ref.shape}")

# --- Run Optimized Implementation (NumPy backend) ---
print("\nRunning optimized implementation (NumPy backend)...")
start = time.time()
tvs_opt, fout_opt, t_out_opt = fborga_2d_opt(
    section, t, fwidth, finc, algorithm="fft", backend="numpy", target_freqs=target_freqs
)
elapsed_opt = time.time() - start
print(f"Optimized fborga_2d time: {elapsed_opt:.3f} s")
print(f"Optimized output shape: {tvs_opt.shape}")

# --- Validation ---
def compare_arrays(arr1, arr2, desc, atol=1e-5, rtol=1e-3):
    if arr1.shape != arr2.shape:
        print(f"{desc}: FAIL - Shape mismatch: {arr1.shape} vs {arr2.shape}")
        return False
    
    max_abs = np.max(np.abs(arr1 - arr2))
    if np.allclose(arr1, arr2, atol=atol, rtol=rtol):
        print(f"{desc}: PASS  (max abs diff = {max_abs:.2e})")
        return True
    else:
        print(f"{desc}: FAIL  (max abs diff = {max_abs:.2e})")
        return False

print("\n--- Validation Results ---")
shape_match = tvs_ref.shape == tvs_opt.shape
print(f"Shape match: {'PASS' if shape_match else 'FAIL'}")

if shape_match:
    compare_arrays(tvs_ref, tvs_opt, "Original vs Optimized (NumPy)")
    compare_arrays(fout_ref, fout_opt, "Frequency arrays")
    compare_arrays(t_out_ref, t_out_opt, "Time arrays")
    
    # Test a few specific values
    print(f"\nSample values comparison:")
    print(f"tvs_ref[100, 2, 10] = {tvs_ref[100, 2, 10]:.6f}")
    print(f"tvs_opt[100, 2, 10] = {tvs_opt[100, 2, 10]:.6f}")
    print(f"Difference = {abs(tvs_ref[100, 2, 10] - tvs_opt[100, 2, 10]):.2e}")
    
    speedup = elapsed_ref / elapsed_opt
    print(f"\nSpeedup: {speedup:.2f}x")
else:
    print(f"Cannot compare values due to shape mismatch!")
    print(f"Expected shape: {tvs_ref.shape}")
    print(f"Actual shape: {tvs_opt.shape}")

print("\nTest complete.")