#!/usr/bin/env python3
"""
Debug script to understand the FFT frequency array.
"""

import numpy as np
from scipy.fft import rfftfreq

# Test parameters
nt = 1024
dt = 0.002

# Calculate like the original
n_padded = 2**int(np.ceil(np.log2(nt)))  # nextpow2
f = rfftfreq(n_padded, dt)

print(f"nt = {nt}")
print(f"n_padded = {n_padded}")
print(f"Number of frequencies: {len(f)}")
print(f"Frequency range: {f[0]} to {f[-1]} Hz")
print(f"First 10 frequencies: {f[:10]}")
print(f"Last 10 frequencies: {f[-10:]}")
print(f"Frequency increment: {f[1] - f[0]}")

# Test frequency generation with this range
fmin, fmax = float(f[0]), float(f[-1])
finc = 2.0

nwin = round((fmax - fmin) / finc) + 1
finc_adjusted = (fmax - fmin) / (nwin - 1) if nwin > 1 else finc
centres = np.linspace(fmin, fmax, nwin)

print(f"\nFrequency generation:")
print(f"fmin = {fmin}, fmax = {fmax}")
print(f"nwin = {nwin}")
print(f"finc_adjusted = {finc_adjusted}")
print(f"First 10 centres: {centres[:10]}")

# Test target frequency matching
target_freqs = [5, 10, 15, 20, 25, 30]
indices = np.array([np.argmin(np.abs(centres - f_target)) for f_target in target_freqs])
closest_freqs = centres[indices]
print(f"\nTarget frequencies: {target_freqs}")
print(f"Closest frequencies: {closest_freqs}")
print(f"Differences: {np.array(target_freqs) - closest_freqs}")