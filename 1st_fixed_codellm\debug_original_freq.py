#!/usr/bin/env python3
"""
Debug script to understand what the original implementation actually does.
"""

import numpy as np
from fborga_2d_3d import fborga

# Test parameters
nt = 1024
dt = 0.002
t = np.arange(nt) * dt
signal = np.random.randn(nt)

print("=== Original Implementation Debug ===")

# Call the original fborga function directly
tvs, fout, t_out = fborga(signal, t, fwidth=10.0, finc=2.0)

print(f"Original fborga output:")
print(f"tvs shape: {tvs.shape}")
print(f"Number of frequencies: {len(fout)}")
print(f"First 10 frequencies: {fout[:10]}")
print(f"Last 10 frequencies: {fout[-10:]}")
print(f"Frequency increment (first few): {np.diff(fout[:10])}")

# Test with target frequencies
target_freqs = [5, 10, 15, 20, 25, 30]
indices = np.array([np.argmin(np.abs(fout - f_target)) for f_target in target_freqs])
closest_freqs = fout[indices]
print(f"\nTarget frequencies: {target_freqs}")
print(f"Closest frequencies: {closest_freqs}")
print(f"Differences: {np.array(target_freqs) - closest_freqs}")