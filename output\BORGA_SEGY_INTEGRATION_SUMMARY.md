# Borga Transform SEG-Y Integration Summary

## Overview
Successfully integrated the proven SEG-Y loading methodology from `6_Run_multispectral_coherence.py` into `4c_Testing_Fborga_2d_3d_translate_sgy.py`, enabling the Borga transform functionality to work with real SEG-Y files.

## Key Changes Made

### 1. Enhanced Imports
Added necessary imports for SEG-Y loading and GUI functionality:
```python
import os
import time
import util
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, ttk
import segyio
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.colors as colors
```

### 2. User Input Function (`get_user_inputs()`)
- **File Selection**: Supports SEG-Y (.sgy, .segy) and NumPy (.npy) files
- **Output Options**: Optional saving of Borga spectral voices
- **Parameter Input**: Interactive dialogs for:
  - Target frequencies (comma-separated list)
  - Gaussian window width (fwidth)
  - Frequency increment (finc)
- **Validation**: Input validation with error handling and retry logic

### 3. Main Workflow Function (`main()`)
Comprehensive workflow that includes:

#### Data Loading
- Uses `util.load_seismic_data()` for robust SEG-Y/NumPy loading
- Handles missing sample intervals for NumPy files
- Supports multiple fallback mechanisms for non-standard SEG-Y files
- Comprehensive error handling for file loading issues

#### Data Format Handling
- **2D Data**: Transposes from (trace, time) to (time, trace) for Borga functions
- **3D Data**: Transposes from (inline, xline, time) to (time, inline, xline)
- **Geometry Detection**: Automatically detects and handles 2D vs 3D data
- **Edge Cases**: Handles single inline/crossline from 3D sources

#### Borga Transform Processing
- Applies appropriate transform based on detected geometry
- Uses target frequency selection for efficient processing
- Provides progress feedback and timing information

#### Results Saving
- Saves spectral voices as NumPy arrays
- Includes comprehensive metadata:
  - Frequencies used
  - Time axis
  - Processing parameters
  - Original data geometry and shape
  - Sample interval (dt)

#### Visualization
- **2D Data**: Shows original seismic and all spectral voices
- **3D Data**: Shows middle inline slice of original and spectral voices
- Interactive plots with proper scaling and colorbars
- Embedded in tkinter window with close button

### 4. Preserved Original Functionality
- **Synthetic Demo**: Original demonstration with synthetic data preserved as `run_synthetic_demo()`
- **Core Functions**: All original Borga transform functions (`fborga`, `fborga_2d`, `fborga_3d`) unchanged
- **Helper Functions**: All mathematical and signal processing functions preserved

### 5. Enhanced Main Execution
- **User Choice**: Dialog to choose between SEG-Y workflow or synthetic demo
- **Graceful Exit**: Proper handling of user cancellation
- **Error Recovery**: Comprehensive error handling throughout

## Data Flow

### Input Processing
1. User selects SEG-Y or NumPy file
2. `util.load_seismic_data()` loads and analyzes the file
3. Geometry detection (2D/3D) and data validation
4. Sample interval handling (from file or user input)

### Transform Processing
1. Data transposition to match Borga function expectations
2. Application of appropriate Borga transform (2D or 3D)
3. Target frequency selection and processing
4. Progress monitoring and timing

### Output Processing
1. Optional saving of results with metadata
2. Comprehensive visualization of original and transformed data
3. Interactive display with proper scaling

## Key Benefits

### Robustness
- Multiple fallback mechanisms for SEG-Y loading
- Comprehensive error handling and user feedback
- Support for non-standard SEG-Y files with custom byte locations

### Usability
- Intuitive GUI dialogs for all user inputs
- Clear progress feedback and timing information
- Professional visualization with proper scaling

### Flexibility
- Supports both 2D and 3D seismic data
- Works with SEG-Y and NumPy files
- Preserves original synthetic demo capability
- Configurable processing parameters

### Data Integrity
- Proper data format handling and transposition
- Metadata preservation and documentation
- Consistent axis conventions throughout

## Usage Instructions

### Running the Enhanced Script
1. Execute `python 4c_Testing_Fborga_2d_3d_translate_sgy.py`
2. Choose between SEG-Y workflow (recommended) or synthetic demo
3. For SEG-Y workflow:
   - Select input SEG-Y file
   - Choose whether to save output
   - Enter Borga parameters (frequencies, fwidth, finc)
   - View results in interactive plot

### File Format Support
- **SEG-Y Files**: .sgy, .segy with automatic geometry detection
- **NumPy Files**: .npy with user-provided sample interval
- **Output**: .npy format with separate metadata file

### Parameter Guidelines
- **Target Frequencies**: Comma-separated list (e.g., "5, 10, 15, 20, 25, 30")
- **fwidth**: Gaussian window width in Hz (e.g., 10)
- **finc**: Frequency increment in Hz (e.g., 2)

## Technical Notes

### Data Axis Conventions
- **util.load_seismic_data()**: Returns (inline, xline, time) for 3D, (trace, time) for 2D
- **fborga_3d()**: Expects (time, inline, xline)
- **fborga_2d()**: Expects (time, trace)
- **Automatic transposition**: Handled seamlessly in the workflow

### Memory Considerations
- Large 3D volumes may require significant memory
- Progress bars provide feedback for long-running operations
- Dask integration from original util functions provides memory efficiency

### Error Handling
- File loading errors with specific guidance
- Parameter validation with retry options
- Graceful handling of user cancellation
- Comprehensive logging and user feedback

## Files Modified
- **Primary**: `4c_Testing_Fborga_2d_3d_translate_sgy.py` - Enhanced with SEG-Y loading
- **Dependencies**: `util.py` - Provides robust SEG-Y loading functions
- **Reference**: `6_Run_multispectral_coherence.py` - Source of proven SEG-Y methodology

## Conclusion
The integration successfully combines the robust SEG-Y loading capabilities from the multispectral coherence script with the Borga transform functionality, creating a comprehensive tool for seismic spectral decomposition that can handle real-world SEG-Y files with professional-grade error handling and visualization.
