#!/usr/bin/env python3
"""
Simple test to verify the optimized implementation works.
"""

import numpy as np
import time

# Import both implementations
from fborga_2d_3d import fborga_2d as fborga_2d_orig
from fborga_2d_3d_optimized_fixed import fborga_2d as fborga_2d_opt

print("== Simple Borga Transform Test ==")

# Generate simple synthetic data
nt, ntr = 256, 10  # Smaller for faster testing
dt = 0.002
t = np.arange(nt) * dt
section = np.random.randn(nt, ntr)

fwidth = 10.0
finc = 2.0
target_freqs = [10, 20, 30]  # Just 3 frequencies

print(f"Section shape: {section.shape}")
print(f"Target frequencies: {target_freqs}")

# Test original implementation
print("\nTesting original implementation...")
try:
    start = time.time()
    tvs_ref, fout_ref, t_out_ref = fborga_2d_orig(
        section, t, fwidth, finc, padflag=1, target_freqs=target_freqs
    )
    elapsed_ref = time.time() - start
    print(f"Original: SUCCESS - Time: {elapsed_ref:.3f}s, Shape: {tvs_ref.shape}")
    print(f"Frequencies: {fout_ref}")
except Exception as e:
    print(f"Original: FAILED - {e}")
    tvs_ref = None

# Test optimized implementation
print("\nTesting optimized implementation...")
try:
    start = time.time()
    tvs_opt, fout_opt, t_out_opt = fborga_2d_opt(
        section, t, fwidth, finc, algorithm="fft", backend="numpy", 
        padflag=True, target_freqs=target_freqs
    )
    elapsed_opt = time.time() - start
    print(f"Optimized: SUCCESS - Time: {elapsed_opt:.3f}s, Shape: {tvs_opt.shape}")
    print(f"Frequencies: {fout_opt}")
except Exception as e:
    print(f"Optimized: FAILED - {e}")
    tvs_opt = None

# Compare if both succeeded
if tvs_ref is not None and tvs_opt is not None:
    freq_match = np.allclose(fout_ref, fout_opt, atol=1e-10)
    print(f"\nFrequency match: {freq_match}")
    
    # Check for NaN/inf in results
    ref_has_nan = np.any(np.isnan(tvs_ref)) or np.any(np.isinf(tvs_ref))
    opt_has_nan = np.any(np.isnan(tvs_opt)) or np.any(np.isinf(tvs_opt))
    
    print(f"Original has NaN/inf: {ref_has_nan}")
    print(f"Optimized has NaN/inf: {opt_has_nan}")
    
    if not ref_has_nan and not opt_has_nan:
        tvs_match = np.allclose(tvs_ref, tvs_opt, atol=1e-3, rtol=1e-2)
        max_diff = np.max(np.abs(tvs_ref - tvs_opt))
        print(f"TVS match: {tvs_match}, Max diff: {max_diff:.2e}")
    else:
        print("Cannot compare TVS due to NaN/inf values")

print("\nTest complete.")