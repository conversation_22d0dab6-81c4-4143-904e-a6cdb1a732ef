#!/usr/bin/env python3
"""
Test script to compare original and optimized implementations using NumPy.
"""

import numpy as np
import matplotlib.pyplot as plt
import time

# Import both implementations
from fborga_2d_3d import fborga_2d as fborga_2d_orig
from fborga_2d_3d_optimized_fixed import fborga_2d as fborga_2d_opt

print("== Borga Transform Comparison Test ==")

# Generate synthetic data
nt, ntr = 1024, 50
f1, f2 = 15, 55  # Hz
dt = 0.002  # 2 ms

t = np.arange(nt) * dt
section = (
    np.sin(2 * np.pi * f1 * t[:, None]) * np.exp(-((t[:, None] - 1.0) / 0.3) ** 2) +
    np.sin(2 * np.pi * f2 * t[:, None]) * np.exp(-((t[:, None] - 1.5) / 0.2) ** 2) +
    0.1 * np.random.randn(nt, ntr)
)

fwidth = 10.0
finc = 2.0
target_freqs = [5, 10, 15, 20, 25, 30]  # Hz

print(f"Section shape: {section.shape}, dt: {dt}")
print(f"Target frequencies: {target_freqs} Hz, fwidth: {fwidth}, finc: {finc}")

# Test original implementation
print("\n=== Original Implementation ===")
start = time.time()
tvs_ref, fout_ref, t_out_ref = fborga_2d_orig(
    section, t, fwidth, finc, padflag=1, target_freqs=target_freqs
)
elapsed_ref = time.time() - start
print(f"Original fborga_2d (CPU) time: {elapsed_ref:.3f} s")
print(f"Output shape: {tvs_ref.shape}")
print(f"Frequencies: {fout_ref}")

# Test optimized implementation with NumPy backend
print("\n=== Optimized Implementation (NumPy) ===")
start = time.time()
tvs_opt, fout_opt, t_out_opt = fborga_2d_opt(
    section, t, fwidth, finc, algorithm="fft", backend="numpy", 
    padflag=True, target_freqs=target_freqs
)
elapsed_opt = time.time() - start
print(f"Optimized fborga_2d (NumPy) time: {elapsed_opt:.3f} s")
print(f"Output shape: {tvs_opt.shape}")
print(f"Frequencies: {fout_opt}")

# Compare results
def compare_arrays(arr1, arr2, desc, atol=1e-5, rtol=1e-3):
    if arr1.shape != arr2.shape:
        print(f"{desc}: SHAPE MISMATCH - {arr1.shape} vs {arr2.shape}")
        return False
    
    # Check for NaN or inf values
    if np.any(np.isnan(arr1)) or np.any(np.isinf(arr1)):
        print(f"{desc}: Original contains NaN/inf values")
    if np.any(np.isnan(arr2)) or np.any(np.isinf(arr2)):
        print(f"{desc}: Optimized contains NaN/inf values")
    
    # Replace NaN/inf with zeros for comparison
    arr1_clean = np.nan_to_num(arr1, nan=0.0, posinf=0.0, neginf=0.0)
    arr2_clean = np.nan_to_num(arr2, nan=0.0, posinf=0.0, neginf=0.0)
    
    is_close = np.allclose(arr1_clean, arr2_clean, atol=atol, rtol=rtol)
    max_diff = np.max(np.abs(arr1_clean - arr2_clean))
    rel_diff = max_diff / (np.max(np.abs(arr1_clean)) + 1e-10)
    
    print(f"{desc}: {'PASS' if is_close else 'FAIL'} - Max diff: {max_diff:.2e}, Rel diff: {rel_diff:.2e}")
    return is_close

print("\n=== Comparison Results ===")
freq_match = compare_arrays(fout_ref, fout_opt, "Frequencies", atol=1e-10, rtol=1e-10)
tvs_match = compare_arrays(tvs_ref, tvs_opt, "TVS arrays", atol=1e-3, rtol=1e-2)
time_match = compare_arrays(t_out_ref, t_out_opt, "Time arrays", atol=1e-10, rtol=1e-10)

print(f"\nSpeedup: {elapsed_ref / elapsed_opt:.2f}x")

# Test without target frequencies
print("\n=== Test Without Target Frequencies ===")
print("Original implementation...")
start = time.time()
tvs_ref_full, fout_ref_full, t_out_ref_full = fborga_2d_orig(
    section, t, fwidth, finc, padflag=1, target_freqs=None
)
elapsed_ref_full = time.time() - start
print(f"Original time: {elapsed_ref_full:.3f} s, shape: {tvs_ref_full.shape}")

print("Optimized implementation...")
start = time.time()
tvs_opt_full, fout_opt_full, t_out_opt_full = fborga_2d_opt(
    section, t, fwidth, finc, algorithm="fft", backend="numpy", 
    padflag=True, target_freqs=None
)
elapsed_opt_full = time.time() - start
print(f"Optimized time: {elapsed_opt_full:.3f} s, shape: {tvs_opt_full.shape}")

print("\n=== Full Comparison Results ===")
freq_match_full = compare_arrays(fout_ref_full, fout_opt_full, "Full frequencies", atol=1e-10, rtol=1e-10)
tvs_match_full = compare_arrays(tvs_ref_full, tvs_opt_full, "Full TVS arrays", atol=1e-3, rtol=1e-2)

print(f"\nFull speedup: {elapsed_ref_full / elapsed_opt_full:.2f}x")

# Summary
print("\n=== SUMMARY ===")
print(f"Target frequencies test: {'PASS' if freq_match and tvs_match and time_match else 'FAIL'}")
print(f"Full frequencies test: {'PASS' if freq_match_full and tvs_match_full else 'FAIL'}")
print(f"Overall: {'PASS' if all([freq_match, tvs_match, time_match, freq_match_full, tvs_match_full]) else 'FAIL'}")