# -*- coding: utf-8 -*-
"""
Created on Sat Jul 20 23:39:38 2024

@author: mutia
"""
# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]
        
import numpy as np
import matplotlib.pyplot as plt
import segyio
from scipy.signal import hilbert
from scipy.ndimage import uniform_filter1d
from sklearn.utils import resample
from libvmd.vmd import vmd2
import time

def read_segy(file_path, revision=1, dsf=5):
    with segyio.open(file_path, "r", ignore_geometry=True) as segy:
        data = segy.trace.raw[:]
        segy_trace_headers = [dict(header) for header in segy.header]
        segy_header = segy.text[0]
    return data, segy_trace_headers, segy_header

def env(x):
    return np.abs(hilbert(x))

def format_time(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{seconds:05.2f}"

# Read SEG-Y file
file_path = r'D:\OneDrive - PT Pertamina (Persero)\12_Matlab_PKB\PKB\RESIDUAL_IMP_VMD\input\ARB_LINE\ARB_2014_OBC_PP_PSDM_FULL_NO_Q.sgy'
Data, SegyTraceHeaders, SegyHeader = read_segy(file_path)

# Process time data
dt = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_INTERVAL] / 1000000
num_samples = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_COUNT]
tseis = np.arange(num_samples) * dt * 1000 - dt

seis_avoa = np.transpose(Data)
cdp_seis = seis_avoa.shape[1]
cdp_num = np.arange(1, cdp_seis + 1)

# 2D VMD parameters
alpha = 1000
tau = 0
K = 3
DC = True
init = 0
tol = 1e-6
N= 500
mirror_extension = False

twt_ms = tseis

# Start timing
start_time = time.time()

print("Starting 2D VMD process...")

# 2D process find operator by random resampling
rat = 0.5
pwr_env = 1
sm = 400
ncomp = K

# Resample the data
bnm = int(cdp_seis * rat)
seis_samp = resample(seis_avoa.T, n_samples=bnm, random_state=42).T

# Perform 2D VMD on the resampled data
u_samp, _, _ = vmd2(seis_samp, K, alpha, tau, DC, init, tol,mirror_extension = mirror_extension)

# Plot each component
fig, axes = plt.subplots(ncomp, 1, figsize=(10, 15))
for i in range(ncomp):
    im = axes[i].imshow(u_samp[i], aspect='auto', cmap='seismic')
    axes[i].set_title(f'Component {i+1}')
    fig.colorbar(im, ax=axes[i])

plt.tight_layout()
plt.show()

# End timing
end_time = time.time()
elapsed_time = format_time(end_time - start_time)
print(f"2D VMD process completed in {elapsed_time}")

# Calculate envelope
env_trc = np.zeros((seis_samp.shape[0], seis_samp.shape[1], ncomp))
for k in range(ncomp):
    env_trc[:, :, k] = env(u_samp[:, :, k]) ** pwr_env

# Calculate average envelope
avg_env = np.zeros((seis_samp.shape[0], ncomp))
for k in range(ncomp):
    out_sum = np.mean(env_trc[:, :, k], axis=1) + 0.01  # teta = 0.01
    avg_env[:, k] = uniform_filter1d(out_sum, size=sm)

# Perform 2D VMD on the entire seismic data
u, _, _ = vmd2(seis_avoa, K, alpha, tau, DC, init, tol,mirror_extension = mirror_extension)


# 2D VMD normalization
seis_bal = np.zeros_like(seis_avoa)
for k in range(ncomp):
    env_trc = uniform_filter1d(env(u[:, :, k]) ** pwr_env, size=sm, axis=0)
    seis_bal += avg_env[:, k][:, np.newaxis] * (u[:, :, k] / (env_trc * (1 + 0.01)))  # teta = 0.01

# End timing
end_time = time.time()
total_time = end_time - start_time

print(f"2D VMD process and normalization completed.")
print(f"Total processing time: {format_time(total_time)}")

Data_bal = seis_bal[:len(tseis), :]
Data_bal[np.isnan(Data_bal)] = 0

# Plot comparison
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
fig.suptitle('Seismic Comparison')

# Adjust colormap limits
vmin = -1500
vmax = 1500

ax1.imshow(Data.T, aspect='auto', cmap='seismic', vmin=vmin, vmax=vmax, extent=[cdp_num.min(), cdp_num.max(), twt_ms.max(), twt_ms.min()])
ax1.set_title('Original')
ax1.set_xlabel('CDP')
ax1.set_ylabel('Time (ms)')

ax2.imshow(Data_bal, aspect='auto', cmap='seismic', vmin=vmin, vmax=vmax, extent=[cdp_num.min(), cdp_num.max(), twt_ms.max(), twt_ms.min()])
ax2.set_title('Balanced')
ax2.set_xlabel('CDP')
ax2.set_ylabel('Time (ms)')

plt.tight_layout()
plt.show()

# Plot decomposed modes and their amplitude spectra
fig, axes = plt.subplots(K, 2, figsize=(20, 6*K))
fig.suptitle('2D VMD Decomposition and Amplitude Spectra')

# Compute frequency axis
fs = 1 / dt  # Sampling frequency
freq = np.fft.fftfreq(num_samples, d=dt)
freq = freq[:num_samples//2]  # Only positive frequencies

for k in range(K):
    # Plot mode
    im = axes[k, 0].imshow(u[:, :, k], aspect='auto', cmap='seismic', vmin=-200, vmax=200, 
                           extent=[cdp_num.min(), cdp_num.max(), twt_ms.max(), twt_ms.min()])
    axes[k, 0].set_title(f'Mode {k+1}')
    axes[k, 0].set_xlabel('CDP')
    axes[k, 0].set_ylabel('Time (ms)')
    plt.colorbar(im, ax=axes[k, 0])
    
    # Compute and plot amplitude spectrum
    # We'll use the average spectrum across all CDPs
    spectrum = np.mean(np.abs(np.fft.fft(u[:, :, k], axis=0)), axis=1)
    spectrum = spectrum[:num_samples//2]  # Only positive frequencies
    
    axes[k, 1].plot(freq, spectrum)
    axes[k, 1].set_title(f'Amplitude Spectrum of Mode {k+1}')
    axes[k, 1].set_xlabel('Frequency (Hz)')
    axes[k, 1].set_ylabel('Amplitude')
    axes[k, 1].set_xlim(0, fs/2)  # Set x-axis limit to Nyquist frequency
    axes[k, 1].grid(True)

plt.tight_layout()
plt.show()