# -*- coding: utf-8 -*-
"""
Created on Mon Jul  8 19:00:08 2024

@author: devri.agustianto
"""
# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]
        
import numpy as np
import matplotlib.pyplot as plt
import segyio
from scipy.signal import hilbert
from scipy.ndimage import uniform_filter1d
from sklearn.utils import resample

import sys
# sys.path.append('C:/Users/<USER>/Documents/OneDrive - PT Pertamina (Persero)/13_Python_PKB/EXT_SCRIPT/fborga')
sys.path.append('C:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/EXT_SCRIPT/fborga')
# sys.path.append('D:/OneDrive - PT Pertamina (Persero)/13_Python_PKB/EXT_SCRIPT/fborga')
from fborga_gmn import fborga as fborga

# from fborga import fborga as fborga# Assuming fborga is in a separate file
# from fborga import fborga_fftw as ffborga

def read_segy(file_path, revision=1, dsf=5):
    with segyio.open(file_path, "r", ignore_geometry=True) as segy:
        data = segy.trace.raw[:]
        segy_trace_headers = [dict(header) for header in segy.header]  # Changed this line
        segy_header = segy.text[0]
    return data, segy_trace_headers, segy_header

def smooth(x, window_len=11, window='hanning'):
    s = np.r_[x[window_len-1:0:-1], x, x[-2:-window_len-1:-1]]
    w = getattr(np, window)(window_len)
    y = np.convolve(w/w.sum(), s, mode='valid')
    return y

def env(x):
    return np.abs(hilbert(x))

def plot_comparison(original, transformed, name):
    sum_transformed = np.sum(transformed, axis=1)
    diff = original - sum_transformed
    max_diff = np.max(np.abs(diff))
    
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15))
    
    ax1.plot(twt_ms, original, label='Original')
    ax1.plot(twt_ms, sum_transformed, label='Sum of Transformed')
    ax1.set_title(f'{name} - Original vs Sum of Transformed')
    ax1.set_xlabel('Time (ms)')
    ax1.set_ylabel('Amplitude')
    ax1.legend()
    
    ax2.plot(twt_ms, diff)
    ax2.set_title(f'{name} - Difference (Original - Sum of Transformed)')
    ax2.set_xlabel('Time (ms)')
    ax2.set_ylabel('Amplitude Difference')
    
    ax3.plot(twt_ms, np.abs(diff))
    ax3.set_title(f'{name} - Absolute Difference')
    ax3.set_xlabel('Time (ms)')
    ax3.set_ylabel('Absolute Amplitude Difference')
    
    plt.tight_layout()
    plt.show()
    
    print(f"Max difference for {name}: {max_diff}")
    if np.allclose(original, sum_transformed, rtol=1e-5, atol=1e-8):
        print(f"Sum of {name} matches the original input within tolerance.")
    else:
        print(f"Warning: Sum of {name} does not match the original input within tolerance.")

# Read SEG-Y file
# file_path = (
#     r'C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\12_Matlab_PKB\PKB\RESIDUAL_IMP_VMD\input\ARB_LINE\ARB_2014_OBC_PP_PSDM_FULL_NO_Q.sgy')
# file_path = (
#     r'D:\OneDrive - PT Pertamina (Persero)\12_Matlab_PKB\PKB\RESIDUAL_IMP_VMD\input\ARB_LINE\ARB_2014_OBC_PP_PSDM_FULL_NO_Q.sgy')
file_path = (
    r'C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\12_Matlab_PKB\PKB\RESIDUAL_IMP_VMD\input\ARB_LINE\ARB_2014_OBC_PP_PSDM_FULL_NO_Q.sgy')
Data, SegyTraceHeaders, SegyHeader = read_segy(file_path)

# Process time data
dt = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_INTERVAL] / 1000000
num_samples = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_COUNT]  # Changed this line
tseis = np.arange(num_samples) * dt * 1000 - dt

seis_avoa = np.transpose(Data)
inpt_west = seis_avoa[:, 179]  # Python uses 0-based indexing
inpt_mid = seis_avoa[:, 543]
inpt_east = seis_avoa[:, 865]
cdp_seis = seis_avoa.shape[1]
cdp_num = np.arange(1, cdp_seis + 1)

# Borga transform parameters
fwidth = 10
finc = 20
padflag = 0
teta = 0.05
twt_ms = tseis

# Perform Borga transform
tvsb_west, foutb_west, tout = fborga(inpt_west, twt_ms / 1000, fwidth, finc, padflag)
tvsb_mid, foutb_mid, _ = fborga(inpt_mid, twt_ms / 1000, fwidth, finc, padflag)
tvsb_east, foutb_east, _ = fborga(inpt_east, twt_ms / 1000, fwidth, finc, padflag)

# Check output
sum_west = np.sum(tvsb_west, axis=1)
sum_east = np.sum(tvsb_east, axis=1)
sum_mid = np.sum(tvsb_mid, axis=1)

# Plot results
fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
fig.suptitle('TVSB')
ax1.imshow(tvsb_west, aspect='auto', cmap='seismic', vmin=-200, vmax=200, extent=[foutb_west.min(), foutb_west.max(), tout.max(), tout.min()])
ax1.set_title('West')
ax2.imshow(tvsb_mid, aspect='auto', cmap='seismic', vmin=-200, vmax=200, extent=[foutb_mid.min(), foutb_mid.max(), tout.max(), tout.min()])
ax2.set_title('Mid')
ax3.imshow(tvsb_east, aspect='auto', cmap='seismic', vmin=-200, vmax=200, extent=[foutb_east.min(), foutb_east.max(), tout.max(), tout.min()])
ax3.set_title('East')
plt.show()

# PLOT COMPARISON
plot_comparison(inpt_west, tvsb_west, "West")
plot_comparison(inpt_mid, tvsb_mid, "Mid")
plot_comparison(inpt_east, tvsb_east, "East")

# 2D process find operator by the random resample trace
rat = 0.5
pwr_env = 1
sm = 400
ncomp = tvsb_east.shape[1]

# Transpose the array, resample along the new first axis, then transpose back
bnm = int(cdp_seis * rat)
seis_samp = resample(seis_avoa.T, n_samples=bnm, random_state=42).T

env_trc = np.zeros((len(twt_ms), ncomp, seis_samp.shape[1]))

for kk in range(seis_samp.shape[1]):
    trc_samp = seis_samp[:, kk]
    tvsb_samp, _, _ = fborga(trc_samp, twt_ms / 1000, fwidth, finc, padflag)
    env_trc[:, :, kk] = env(tvsb_samp) ** pwr_env

tsmooth = np.arange(1, tvsb_samp.shape[0] + 1)

avg_env = np.zeros((len(tsmooth), ncomp))

for kk in range(ncomp):
    out_sum = np.mean(env_trc[:, kk, :], axis=1) + teta
    avg_env[:, kk] = uniform_filter1d(out_sum, size=sm)

# Borga normalization
# lt = 300
# zz = 8
seis_bal = np.zeros_like(seis_avoa)
for lt in range(cdp_seis):
    trc_inpt = seis_avoa[:, lt]
    tvsb_out, _, _ = fborga(trc_inpt, twt_ms / 1000, fwidth, finc, padflag)
    out_bal = np.zeros_like(tvsb_out)
    for zz in range(ncomp):
        env_trc = uniform_filter1d(env(tvsb_out[:, zz]) ** pwr_env, size=sm)
        out_bal[:, zz] = avg_env[:, zz] * (tvsb_out[:, zz] / (env_trc * (1 + teta)))
    seis_bal[:, lt] = np.sum(out_bal, axis=1)

# intrace = tvsb_out[:, zz]
# xtrace = out_bal[:, zz]

# # Plot data sets on the same subplot
# plt.plot(tseis,intrace, label="input")
# plt.plot(tseis,xtrace, label="output")

Data_bal = seis_bal[:len(tseis), :]
Data_bal[np.isnan(Data_bal)] = 0

# Plot comparison
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 10))
fig.suptitle('Seismic Comparison')

# Adjust colormap limits
vmin = -1500
vmax = 1500

ax1.imshow(Data.T, aspect='auto', cmap='seismic', vmin=vmin, vmax=vmax, extent=[cdp_num.min(), cdp_num.max(), tout.max(), tout.min()])
ax1.set_title('Original')
ax2.imshow(Data_bal, aspect='auto', cmap='seismic', vmin=vmin, vmax=vmax, extent=[cdp_num.min(), cdp_num.max(), tout.max(), tout.min()])
ax2.set_title('Balanced')

plt.show()