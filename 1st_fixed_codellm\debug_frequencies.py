#!/usr/bin/env python3
"""
Debug script to compare frequency arrays between implementations.
"""

import numpy as np

from fborga_2d_3d import fborga_2d as fborga_2d_orig  # Original
from fborga_2d_3d_optimized_fixed import fborga_2d as fborga_2d_opt

# --- Synthetic Data for Testing ---
nt, ntr = 1024, 50
dt = 0.002  # 2 ms
t = np.arange(nt) * dt
section = np.random.randn(nt, ntr)

# --- Parameters ---
fwidth = 10.0
finc = 2.0
target_freqs = [5, 10, 15, 20, 25, 30]  # Hz

print("=== Frequency Array Comparison ===")

# --- Run Original Implementation ---
print("Running original implementation...")
tvs_ref, fout_ref, t_out_ref = fborga_2d_orig(
    section, t, fwidth, finc, target_freqs=target_freqs
)
print(f"Original frequencies: {fout_ref}")

# --- Run Optimized Implementation ---
print("\nRunning optimized implementation...")
tvs_opt, fout_opt, t_out_opt = fborga_2d_opt(
    section, t, fwidth, finc, algorithm="fft", backend="numpy", target_freqs=target_freqs
)
print(f"Optimized frequencies: {fout_opt}")

print(f"\nFrequency differences: {fout_ref - fout_opt}")
print(f"Max frequency difference: {np.max(np.abs(fout_ref - fout_opt))}")

# Test without target frequencies
print("\n=== Without Target Frequencies ===")
tvs_ref2, fout_ref2, _ = fborga_2d_orig(section, t, fwidth, finc)
tvs_opt2, fout_opt2, _ = fborga_2d_opt(section, t, fwidth, finc, algorithm="fft", backend="numpy")

print(f"Original frequencies (no target): {len(fout_ref2)} frequencies")
print(f"First 10: {fout_ref2[:10]}")
print(f"Optimized frequencies (no target): {len(fout_opt2)} frequencies") 
print(f"First 10: {fout_opt2[:10]}")

if len(fout_ref2) == len(fout_opt2):
    print(f"Max frequency difference (no target): {np.max(np.abs(fout_ref2 - fout_opt2))}")
else:
    print(f"Different number of frequencies: {len(fout_ref2)} vs {len(fout_opt2)}")