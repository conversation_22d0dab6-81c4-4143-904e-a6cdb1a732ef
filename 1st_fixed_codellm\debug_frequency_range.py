#!/usr/bin/env python3
"""
Debug script to understand frequency range calculation.
"""

import numpy as np
from scipy.fft import rfftfreq

# Test parameters
nt = 1024
dt = 0.002
finc = 2.0

# Calculate frequency range like the optimized implementation
nfft = int(2 ** np.ceil(np.log2(nt)))
freqs = rfftfreq(nfft, dt)
fmin, fmax = float(freqs[0]), float(freqs[-1])

print(f"nt = {nt}, dt = {dt}, nfft = {nfft}")
print(f"Frequency range: {fmin} to {fmax} Hz")
print(f"Number of frequency bins: {len(freqs)}")

# Original frequency generation logic
nwin_orig = round((fmax - fmin) / finc) + 1
finc_adjusted = (fmax - fmin) / (nwin_orig - 1) if nwin_orig > 1 else finc
centres_orig = np.linspace(fmin, fmax, nwin_orig)

print(f"\nOriginal logic:")
print(f"nwin = {nwin_orig}")
print(f"finc_adjusted = {finc_adjusted}")
print(f"First 10 frequencies: {centres_orig[:10]}")

# My current logic
nwin_mine = int(np.floor((fmax - fmin) / finc)) + 1
centres_mine = np.linspace(fmin, fmax, nwin_mine)

print(f"\nMy logic:")
print(f"nwin = {nwin_mine}")
print(f"First 10 frequencies: {centres_mine[:10]}")

# Test with target frequencies
target_freqs = [5, 10, 15, 20, 25, 30]
print(f"\nTarget frequencies: {target_freqs}")

# Find closest in original grid
indices = np.array([np.argmin(np.abs(centres_orig - f_target)) for f_target in target_freqs])
closest_orig = centres_orig[indices]
print(f"Closest in original grid: {closest_orig}")

# Find closest in my grid  
indices_mine = np.array([np.argmin(np.abs(centres_mine - f_target)) for f_target in target_freqs])
closest_mine = centres_mine[indices_mine]
print(f"Closest in my grid: {closest_mine}")