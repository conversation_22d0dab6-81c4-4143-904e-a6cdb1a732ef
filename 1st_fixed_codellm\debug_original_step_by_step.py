#!/usr/bin/env python3
"""
Debug script to trace the original implementation step by step.
"""

import numpy as np
from scipy.fft import rfftfreq
from fborga_2d_3d import nextpow2, fftrl, gaussian_upou

# Test parameters
nt = 1024
dt = 0.002
t = np.arange(nt) * dt
signal = np.random.randn(nt)
fwidth = 10.0
finc = 2.0

print("=== Step-by-step Original Implementation ===")

# Step 1: Prepare signal
signal = np.nan_to_num(signal)
nt = len(signal)
original_length = nt
n_padded = 2**nextpow2(nt)

print(f"nt = {nt}")
print(f"nextpow2(nt) = {nextpow2(nt)}")
print(f"n_padded = {n_padded}")

# Step 2: FFT
spectrum, f = fftrl(signal, t, n_padded)
print(f"Number of frequencies from fftrl: {len(f)}")
print(f"Frequency range: {f[0]} to {f[-1]} Hz")
print(f"First 10 frequencies: {f[:10]}")

# Step 3: Gaussian frequency generation
fmin = f[0]
print(f"fmin = {fmin}")

# Call gaussian_upou to get the frequency grid
_, norm_factor, fnotvec, nwin, _ = gaussian_upou(f, fmin, fwidth, finc, gdb=np.inf)

print(f"nwin from gaussian_upou = {nwin}")
print(f"Number of frequencies in fnotvec: {len(fnotvec)}")
print(f"First 10 frequencies from fnotvec: {fnotvec[:10]}")
print(f"Last 10 frequencies from fnotvec: {fnotvec[-10:]}")

# Check the frequency increment
if len(fnotvec) > 1:
    print(f"Frequency increment: {fnotvec[1] - fnotvec[0]}")

# Test target frequency matching
target_freqs = [5, 10, 15, 20, 25, 30]
indices = np.array([np.argmin(np.abs(fnotvec - f_target)) for f_target in target_freqs])
closest_freqs = fnotvec[indices]
print(f"\nTarget frequencies: {target_freqs}")
print(f"Closest frequencies: {closest_freqs}")
print(f"Differences: {np.array(target_freqs) - closest_freqs}")