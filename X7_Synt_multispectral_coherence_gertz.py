# multispectral_pipeline.py

import numpy as np
from tqdm import tqdm

# --- Import the necessary functions from your other scripts ---
# Make sure fborga_2d_3d.py is in the same directory or your Python path
try:
    from fborga_2d_3d import fborga_2d, fborga_3d
except ImportError:
    print("Error: Could not import 'fborga_2d_3d.py'. Make sure the file is in the same directory.")
    exit()

# =============================================================================
# CORE COHERENCE CALCULATION ENGINES
# (These are the functions from the original discontinuity.py, plus our
#  multispectral version)
# =============================================================================

def gersztenkorn(traces):
    """
    BROADBAND Eigenstructure Coherence (Original)
    Gersztenkorn, A., and K. <PERSON><PERSON>, 1999, Eigenstructure‐based coherence
    computations as an aid to 3-D structural and stratigraphic mapping:
    GEOPHYSICS, 64, 1468-1479. doi:10.1190/1.1444651
    
    :param traces: A 3D NumPy array of shape (n_ilines, n_xlines, n_samples)
                   representing a window of BROADBAND seismic data.
    """
    traces = traces.reshape(-1, traces.shape[-1])
    cov = traces.dot(traces.T)
    vals = np.linalg.eigvalsh(cov)
    return vals.max() / (vals.sum() + 1e-12)

def multispectral_gersztenkorn(spectral_voices):
    """
    MULTISPECTRAL Eigenstructure Coherence (Modified)
    Based on the principles from Marfurt (2017) and Li et al. (2018).
    
    :param spectral_voices: A list or tuple of NumPy arrays. Each array is a
                            3D window (n_ilines, n_xlines, n_samples) for ONE
                            spectral voice. All voices must have the same shape.
    """
    n_traces = spectral_voices[0].size // spectral_voices[0].shape[-1]
    dtype = np.complex128 if np.iscomplexobj(spectral_voices[0]) else np.float64
    cov_multi = np.zeros((n_traces, n_traces), dtype=dtype)

    for voice in spectral_voices:
        d_voice = voice.reshape(-1, voice.shape[-1])
        cov_voice = d_voice.dot(d_voice.T.conj())
        cov_multi += cov_voice

    vals = np.linalg.eigvalsh(cov_multi)
    return vals.max().real / (vals.sum().real + 1e-12)


# =============================================================================
# NEW TOP-LEVEL PIPELINE FUNCTION
# =============================================================================

def multispectral_coherence_pipeline(
    seismic_data,
    t,
    window_duration,
    step_out=1,
    start_freq=10,
    num_freqs=3,
    freq_step=20,
    fwidth=10.0,
    finc=5.0
):
    """
    Calculates multispectral coherence by first performing a Borga transform
    for spectral decomposition.

    Parameters
    ----------
    seismic_data : np.ndarray
        2D (n_samples, n_traces) or 3D (n_samples, n_inlines, n_xlines) seismic data.
    t : np.ndarray
        Time vector corresponding to the first axis of seismic_data.
    
    --- Coherence Parameters ---
    window_duration : float
        The vertical length in seconds of the analysis window for coherence.
    step_out : int, optional
        Number of adjacent traces in each spatial direction for the analysis window.
        A 3x3 spatial window corresponds to step_out=1. Default is 1.
        
    --- Spectral Decomposition Parameters ---
    start_freq : float, optional
        The center frequency of the first spectral voice to generate. Default is 10 Hz.
    num_freqs : int, optional
        The total number of spectral voices to use. Default is 3.
    freq_step : float, optional
        The step in Hz between the center frequencies of the voices. Default is 20 Hz.
    fwidth : float, optional
        Gaussian window width for the Borga transform (parameter for fborga). Default is 10.0.
    finc : float, optional
        Frequency increment for the Borga transform (parameter for fborga). Default is 5.0.

    Returns
    -------
    np.ndarray
        A coherence volume with the same spatial dimensions as the input seismic data.
    """
    dt = t[1] - t[0]
    
    # 1. DEFINE TARGET FREQUENCIES for Borga Transform
    target_freqs = np.arange(num_freqs) * freq_step + start_freq
    print(f"Pipeline initiated. Target center frequencies: {target_freqs} Hz")

    # 2. PERFORM SPECTRAL DECOMPOSITION using Borga Transform
    if seismic_data.ndim == 2:
        # Input shape: (n_samples, n_traces)
        # fborga_2d returns shape: (n_samples, n_freqs, n_traces)
        print("\n--- Step 1: Performing 2D Borga Transform ---")
        spectral_volumes, _, _ = fborga_2d(
            seismic_data, t, fwidth, finc, target_freqs=target_freqs
        )
        # Reshape to (n_freqs, n_inlines, n_xlines, n_samples) for processing
        # Add a dummy xline dimension for consistency
        spectral_volumes = np.transpose(spectral_volumes, (1, 2, 0)) # -> (n_freqs, n_traces, n_samples)
        spectral_volumes = spectral_volumes[:, :, np.newaxis, :]      # -> (n_freqs, n_traces, 1, n_samples)

    elif seismic_data.ndim == 3:
        # Input shape: (n_samples, n_inlines, n_xlines)
        # fborga_3d returns shape: (n_samples, n_freqs, n_inlines, n_xlines)
        print("\n--- Step 1: Performing 3D Borga Transform ---")
        spectral_volumes, _, _ = fborga_3d(
            seismic_data, t, fwidth, finc, target_freqs=target_freqs
        )
        # Reshape to (n_freqs, n_inlines, n_xlines, n_samples) for processing
        spectral_volumes = np.transpose(spectral_volumes, (1, 2, 3, 0))

    else:
        raise ValueError("Input seismic_data must be 2D or 3D.")

    print("\n--- Step 2: Calculating Multispectral Coherence ---")
    
    # 3. SETUP COHERENCE CALCULATION WINDOW
    if seismic_data.ndim == 2:
        # Add a dummy dimension to treat 2D data like 3D data
        _, n_inlines, _, n_samples = spectral_volumes.shape
        n_xlines = 1
        i_win = 2 * step_out + 1
        x_win = 1 # Only one trace in xline direction for 2D data
    else:
        _, n_inlines, n_xlines, n_samples = spectral_volumes.shape
        i_win = 2 * step_out + 1
        x_win = 2 * step_out + 1

    t_win = int(window_duration / dt)
    if t_win % 2 == 0: t_win += 1 # Ensure odd window size for a clear center

    i_half, x_half, t_half = i_win // 2, x_win // 2, t_win // 2
    
    output_shape = (n_inlines, n_xlines, n_samples)
    output_coherence = np.zeros(output_shape)

    # 4. SLIDING WINDOW CALCULATION
    # Use tqdm for a progress bar
    total_calculations = (n_inlines - 2 * i_half) * (n_xlines - 2 * x_half) * (n_samples - 2 * t_half)
    pbar = tqdm(total=total_calculations, desc="Calculating Coherence")

    for i in range(i_half, n_inlines - i_half):
        for j in range(x_half, n_xlines - x_half):
            for k in range(t_half, n_samples - t_half):
                
                # Extract the 3D analysis window from EACH spectral voice
                windows_for_this_point = []
                for freq_idx in range(num_freqs):
                    window = spectral_volumes[freq_idx,
                                              i - i_half : i + i_half + 1,
                                              j - x_half : j + x_half + 1,
                                              k - t_half : k + t_half + 1]
                    windows_for_this_point.append(window)

                # Pass the LIST of windows to our coherence function
                coherence_value = multispectral_gersztenkorn(windows_for_this_point)
                
                # Store the result
                output_coherence[i, j, k] = coherence_value
                pbar.update(1)
    
    pbar.close()
    print("\nPipeline finished successfully.")
    return np.squeeze(output_coherence.transpose(2, 0, 1)) # Return to (samples, iline, xline)

# =============================================================================
# EXAMPLE USAGE
# =============================================================================
if __name__ == '__main__':
    import matplotlib.pyplot as plt

    print("Running example usage...")

    # --- Create Synthetic 3D Seismic Data ---
    shape_3d = (200, 50, 50) # (samples, inlines, xlines)
    dt = 0.002
    t_vec = np.arange(shape_3d[0]) * dt
    
    seismic_3d = np.zeros(shape_3d)
    seismic_3d[100:, :, 25:] = 1.0 # Simple block fault
    # Add a dipping event
    x = np.arange(shape_3d[2])
    y = np.arange(shape_3d[1])
    xx, yy = np.meshgrid(x, y)
    dipping_event_time_idx = 50 + (xx + yy) * 0.3
    for i in range(shape_3d[1]):
        for j in range(shape_3d[2]):
             seismic_3d[int(dipping_event_time_idx[i,j]), i, j] = -0.5

    seismic_3d += np.random.randn(*shape_3d) * 0.1 # Add noise
    
    print(f"Generated synthetic 3D data of shape: {seismic_3d.shape}")

    # --- Run the Full Pipeline ---
    coherence_volume_3d = multispectral_coherence_pipeline(
        seismic_data=seismic_3d,
        t=t_vec,
        window_duration=0.020,  # 20 ms vertical window
        step_out=1,             # 3x3 trace spatial window
        start_freq=15,          # First voice centered at 15 Hz
        num_freqs=3,            # Use 3 voices (15, 35, 55 Hz)
        freq_step=20,           # 20 Hz between voices
        fwidth=10,              # Borga param: Gaussian width
        finc=5                  # Borga param: Freq increment
    )
    
    print(f"Output coherence volume shape: {coherence_volume_3d.shape}")

    # --- Visualize the Results ---
    time_slice_idx = 100
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 6), sharey=True)
    
    im1 = axes[0].imshow(seismic_3d[time_slice_idx, :, :].T, cmap='gray_r', aspect='auto')
    axes[0].set_title(f'Original Seismic Time Slice at t={time_slice_idx*dt:.3f}s')
    axes[0].set_xlabel('Inline')
    axes[0].set_ylabel('Crossline')
    fig.colorbar(im1, ax=axes[0], orientation='horizontal', pad=0.1)

    im2 = axes[1].imshow(coherence_volume_3d[time_slice_idx, :, :].T, cmap='gray', vmin=0, vmax=1, aspect='auto')
    axes[1].set_title('Multispectral Coherence Time Slice')
    axes[1].set_xlabel('Inline')
    fig.colorbar(im2, ax=axes[1], orientation='horizontal', pad=0.1)

    plt.suptitle('Multispectral Coherence Pipeline Results')
    plt.tight_layout(rect=[0, 0.05, 1, 0.95])
    plt.show()