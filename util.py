# -*- coding: utf-8 -*-
"""
Utilities for working with seismic and computing volume attributes.

@author: <PERSON><PERSON>-<PERSON>
@email: <EMAIL>

"""

# Import Libraries
import os
import segyio
import dask.array as da
import numpy as np
import h5py
import psutil
import os
import segyio

def compute_chunk_size(shape, byte_size, kernel=None, preview=None):
    """
    Description
    -----------
    Compute ideal block size for Dask Array given specific information about 
    the computer being used, the input data, kernel size, and whether or not
    this operation is is 'preview' mode.
    
    Parameters
    ----------
    shape : tuple (len 3), shape of seismic data
    byte_size : int, byte size of seismic data dtype
    
    Keywork Arguments
    -----------------    
    kernel : tuple (len 3), operator size
    preview : str, enables or disables preview mode and specifies direction
        Acceptable inputs are (None, 'inline', 'xline', 'z')
        Optimizes chunk size in different orientations to facilitate rapid
        screening of algorithm output
    
    Returns
    -------
    chunk_size : tuple (len 3), optimal chunk size
    """
    
    # Evaluate kernel
    if kernel == None:
        kernel = (1,1,1)
        ki, kj, kk = kernel
    else:
        ki, kj, kk = kernel
        
    # Identify acceptable chunk sizes
    i_s = np.arange(ki, shape[0])
    j_s = np.arange(kj, shape[1])
    k_s = np.arange(kk, shape[2])
    
    modi = shape[0] % i_s
    modj = shape[1] % j_s
    modk = shape[2] % k_s
    
    kki = i_s[(modi >= ki) | (modi == 0)]
    kkj = j_s[(modj >= kj) | (modj == 0)]
    kkk = k_s[(modk >= kk) | (modk == 0)]
    
    # Compute Machine Specific information
    mem = psutil.virtual_memory().available
    cpus = psutil.cpu_count()
    byte_size = byte_size     
    M = ((mem / (cpus * byte_size)) / (ki * kj * kk)) * 0.75
    
    # Compute chunk size if preview mode is disabled
    if preview == None:
#        M *= 0.3
        Mij = kki * kkj.reshape(-1,1) * shape[2]
        Mij[Mij > M] = -1
        Mij = Mij.diagonal()

        chunks = [kki[Mij.argmax()], kkj[Mij.argmax()], shape[2]]
    
    # Compute chunk size if preview mode is enabled
    else:        
        kki = kki.min()
        kkj = kkj.min()
        kkk = kkk.min()
        
        if preview == 'inline':
            if (kki * shape[1] * shape[2]) < M:
                chunks = [kki, shape[1], shape[2]]
            
            else:
                j_s = np.arange(kkj, shape[1])
                modj = shape[1] % j_s
                kkj = j_s[(modj >= kj) | (modj == 0)]
                Mj = j_s * kki * shape[2]
                Mj = Mj[Mj < M]
                chunks = [kki, Mj.argmax(), shape[2]]    
                
        elif preview == 'xline':
            if (kkj * shape[0] * shape[2]) < M:
                chunks = [shape[0], kkj, shape[2]]
                
            else:
                i_s = np.arange(kki, shape[0])
                modi = shape[0] % i_s
                kki = i_s[(modi >= ki) | (modi == 0)]
                Mi = i_s * kkj * shape[2]
                Mi = Mi[Mi < M]
                chunks = [Mi.argmax(), kkj, shape[2]]                    
            
        else:
            if (kkk * shape[0] * shape[1]) < M:
                chunks = [shape[0], shape[2], kk]
            
            else:
                j_s = np.arange(kkj, shape[1])
                modj = shape[1] % j_s
                kkj = j_s[(modj >= kj) | (modj == 0)]
                Mj = j_s * kkk * shape[0]
                Mj = Mj[Mj < M]
                chunks = [shape[0], Mj.argmax(), kkk]
        
    return(tuple(chunks))
    
        
        
def trim_dask_array(in_data, kernel):
    """
    Description
    -----------
    Trim resuling Dask Array given a specified kernel size
    
    Parameters
    ----------
    in_data : Dask Array
    kernel : tuple (len 3), operator size
    
    Returns
    -------
    out : Dask Array
    """
    
    # Compute half windows and assign to dict
    hw = tuple(np.array(kernel) // 2)    
    axes = {0 : hw[0], 1 : hw[1], 2: hw[2]}
    
    return(da.ghost.trim_internal(in_data, axes=axes))
    
    

def available_volumes(file_path):
    """
    Description
    -----------
    Convience function to evaluate what volumes exist and what their names are
    
    Parameters
    ----------
    file_path : str, path to file
       
    Returns
    -------
    vols : list, array of volume names in file
    """
    
    # Iterate through HDF5 file and output dataset names
    with h5py.File(file_path) as f:
        vols = [i for i in f]
    
    return(vols)
    


def read(file_path):
    """
    Description
    -----------
    Convience function to read file and create a pointer to data on disk
    
    Parameters
    ----------
    file_path : str, path to file
       
    Returns
    -------
    data : HDF5 dataset, pointer to data on disk
    """
    
    data = h5py.File(file_path)['data']
    
    return(data)


# NEW ROBUST SEISMIC LOADER FUNCTION
def load_seismic_data(path):
    """
    Robustly loads seismic data from SEGY or NumPy files.

    This function intelligently detects the data format and geometry (3D, 2D, etc.)
    and returns a NumPy array with consistent axis ordering, along with essential metadata.

    Axis Convention:
    - 3D Data: (inline, xline, time_sample)
    - 2D Data: (trace, time_sample)

    Parameters
    ----------
    path : str
        The full path to the input file. Supported extensions: .sgy, .segy, .npy.

    Returns
    -------
    seismic_data : numpy.ndarray
        The loaded seismic data. Shape will be 3D or 2D depending on the source.
    metadata : dict
        A dictionary containing key information about the data:
        - 'type' (str): 'segy' or 'numpy'.
        - 'geometry' (str): '3D', '2D', or 'Unknown'.
        - 'shape' (tuple): The shape of the returned `seismic_data` array.
        - 'dt' (float): The sample interval in seconds (e.g., 0.002). Is None for .npy.
        - 'time_axis' (np.array): The time axis in seconds. Is None for .npy.
        - 'segy_spec' (segyio.spec): The segyio spec object, useful for writing output.
                                     Is None for .npy.
        - 'template_path' (str): The original path, to be used as a template for writing.

    Raises
    ------
    FileNotFoundError
        If the specified path does not exist.
    ValueError
        If the file type is not supported.
    IOError
        If there is an issue reading the SEGY file.
    """
    if not os.path.exists(path):
        raise FileNotFoundError(f"The specified file does not exist: {path}")

    _, ext = os.path.splitext(path)
    ext = ext.lower()

    if ext in ['.sgy', '.segy']:
        return _load_from_segy(path)
    elif ext == '.npy':
        return _load_from_numpy(path)
    else:
        raise ValueError(f"Unsupported file type: '{ext}'. Please use .sgy, .segy, or .npy.")

def _load_from_segy(path):
    """Helper function to load data from a SEGY file with fallback for non-standard geometry."""
    print(f"Loading SEGY file: {path}")
    
    # First attempt: Try standard loading with strict=False
    try:
        print("Attempting standard SEGY loading...")
        return _load_segy_standard(path)
    except Exception as first_error:
        print(f"Standard loading failed: {first_error}")
        print("Attempting fallback loading with custom byte locations...")
        
        # Second attempt: Try with user-specified byte locations
        try:
            return _load_segy_with_custom_bytes(path)
        except Exception as second_error:
            print(f"Custom byte loading also failed: {second_error}")
            # Third attempt: Load as unstructured
            return _load_segy_unstructured(path)

def _load_segy_standard(path):
    """Standard SEGY loading approach."""
    with segyio.open(path, strict=False) as f:
        # --- 1. Extract Basic Metadata ---
        if hasattr(f, 'spec') and f.spec is not None:
            segy_spec = f.spec
            dt = segy_spec.format.sample_interval / 1_000_000.0 if segy_spec.format.sample_interval > 0 else 0.002
        else:
            # Fallback if no spec available
            dt = 0.004  # Common default
            segy_spec = None
            
        time_axis = f.samples / 1000.0 if f.samples is not None and len(f.samples) > 0 else None
        if time_axis is None and segy_spec:
            time_axis = np.arange(segy_spec.samples) * dt
        
        # --- 2. Detect Geometry ---
        is_3d = hasattr(f, 'iline') and hasattr(f, 'xline') and f.iline.size > 1 and f.xline.size > 1

        if is_3d:
            print("Detected 3D geometry. Loading as a cube...")
            geometry = '3D'
            # segyio.tools.cube is the most robust way to load a 3D volume
            seismic_data = segyio.tools.cube(path)
            
            # Ensure (IL, XL, T) dimension order
            iline_dim, xline_dim = f.iline.size, f.xline.size
            if seismic_data.shape == (xline_dim, iline_dim, f.samples.size):
                print("Data was (XLINE, ILINE, TIME), transposing to (ILINE, XLINE, TIME).")
                seismic_data = np.transpose(seismic_data, (1, 0, 2))
            
        else: # Handle 2D, arbitrary lines, or single traces
            print("Could not detect standard 3D geometry. Loading as 2D/arbitrary line(s).")
            geometry = '2D'
            # f.trace.raw loads all traces into a (trace_count, sample_count) array
            seismic_data = f.trace.raw[:]
            
    print(f"Successfully loaded {geometry} data with shape {seismic_data.shape}")
    
    metadata = {
        'type': 'segy',
        'geometry': geometry,
        'shape': seismic_data.shape,
        'dt': dt,
        'time_axis': time_axis,
        'segy_spec': segy_spec,
        'template_path': path
    }
    return np.ascontiguousarray(seismic_data), metadata

def _load_segy_with_custom_bytes(path):
    """Load SEGY with user-specified inline/crossline byte locations."""
    import tkinter as tk
    from tkinter import simpledialog, messagebox
    
    # Create a temporary root for dialogs
    temp_root = tk.Tk()
    temp_root.withdraw()
    
    try:
        # Ask user for byte locations
        messagebox.showinfo("SEGY Loading Error", 
                          "Standard SEGY loading failed. This may be because the inline/crossline "
                          "numbers are not in the standard byte locations (189 and 193).\n\n"
                          "Please specify the correct byte locations for your SEGY file.",
                          parent=temp_root)
        
        # Get inline byte location
        inline_byte_str = simpledialog.askstring(
            "Inline Byte Location",
            "Enter the byte location for inline numbers:\n"
            "(Standard is 189, but common alternatives are 9, 17, 181, 185, 221)\n"
            "Check your SEGY file documentation or header analysis tool.",
            initialvalue="189",
            parent=temp_root
        )
        
        if inline_byte_str is None:
            raise ValueError("User cancelled inline byte location input")
        
        inline_byte = int(inline_byte_str)
        
        # Get crossline byte location  
        crossline_byte_str = simpledialog.askstring(
            "Crossline Byte Location", 
            "Enter the byte location for crossline numbers:\n"
            "(Standard is 193, but common alternatives are 13, 21, 185, 189, 225)\n"
            "Usually crossline byte = inline byte + 4",
            initialvalue=str(inline_byte + 4),
            parent=temp_root
        )
        
        if crossline_byte_str is None:
            raise ValueError("User cancelled crossline byte location input")
            
        crossline_byte = int(crossline_byte_str)
        
        # Try opening with custom byte locations
        print(f"Attempting to open SEGY with inline byte {inline_byte} and crossline byte {crossline_byte}")
        
        with segyio.open(path, strict=False, iline=inline_byte, xline=crossline_byte) as f:
            # Extract metadata
            dt = 0.004  # Default fallback
            if hasattr(f, 'spec') and f.spec is not None:
                segy_spec = f.spec
                if hasattr(segy_spec.format, 'sample_interval') and segy_spec.format.sample_interval > 0:
                    dt = segy_spec.format.sample_interval / 1_000_000.0
            else:
                segy_spec = None
                
            time_axis = f.samples / 1000.0 if f.samples is not None and len(f.samples) > 0 else None
            if time_axis is None:
                # Fallback time axis
                num_samples = len(f.trace[0]) if len(f.trace) > 0 else 1000
                time_axis = np.arange(num_samples) * dt
            
            # Check if 3D geometry was successfully detected
            try:
                is_3d = hasattr(f, 'iline') and hasattr(f, 'xline') and f.iline.size > 1 and f.xline.size > 1
                
                if is_3d:
                    print("Successfully detected 3D geometry with custom byte locations!")
                    geometry = '3D'
                    seismic_data = segyio.tools.cube(path, iline=inline_byte, xline=crossline_byte)
                    
                    # Ensure correct dimension order
                    iline_dim, xline_dim = f.iline.size, f.xline.size
                    if seismic_data.shape == (xline_dim, iline_dim, len(time_axis)):
                        print("Transposing from (XLINE, ILINE, TIME) to (ILINE, XLINE, TIME)")
                        seismic_data = np.transpose(seismic_data, (1, 0, 2))
                        
                else:
                    print("Loading as 2D/unstructured data")
                    geometry = '2D'
                    seismic_data = f.trace.raw[:]
                    
            except Exception as cube_error:
                print(f"Cube loading failed, loading as traces: {cube_error}")
                geometry = '2D'
                seismic_data = f.trace.raw[:]
        
        messagebox.showinfo("Success", 
                          f"Successfully loaded SEGY file using:\n"
                          f"Inline byte: {inline_byte}\n"
                          f"Crossline byte: {crossline_byte}\n"
                          f"Geometry: {geometry}\n"
                          f"Shape: {seismic_data.shape}",
                          parent=temp_root)
        
        metadata = {
            'type': 'segy',
            'geometry': geometry,
            'shape': seismic_data.shape,
            'dt': dt,
            'time_axis': time_axis,
            'segy_spec': segy_spec,
            'template_path': path,
            'custom_bytes': {'inline': inline_byte, 'crossline': crossline_byte}
        }
        
        return np.ascontiguousarray(seismic_data), metadata
        
    except ValueError as e:
        if "cancelled" in str(e).lower():
            raise IOError("User cancelled the custom byte location input")
        else:
            raise IOError(f"Invalid byte location input: {e}")
    except Exception as e:
        raise IOError(f"Failed to load SEGY with custom byte locations: {e}")
    finally:
        try:
            temp_root.destroy()
        except:
            pass

def _load_segy_unstructured(path):
    """Last resort: Load SEGY as completely unstructured data."""
    print("Loading SEGY as unstructured data (no geometry information)")
    
    try:
        with segyio.open(path, strict=False, ignore_geometry=True) as f:
            # Load all traces as a 2D array
            seismic_data = f.trace.raw[:]
            
            # Extract basic metadata
            dt = 0.004  # Default fallback
            if hasattr(f, 'spec') and f.spec is not None:
                segy_spec = f.spec
                if hasattr(segy_spec.format, 'sample_interval') and segy_spec.format.sample_interval > 0:
                    dt = segy_spec.format.sample_interval / 1_000_000.0
            else:
                segy_spec = None
            
            # Create time axis
            if len(seismic_data) > 0:
                num_samples = seismic_data.shape[1]
                time_axis = np.arange(num_samples) * dt
            else:
                time_axis = np.array([])
            
            print(f"Loaded unstructured data with shape {seismic_data.shape}")
            
            metadata = {
                'type': 'segy',
                'geometry': '2D',  # Treat as 2D since no geometry detected
                'shape': seismic_data.shape,
                'dt': dt,
                'time_axis': time_axis,
                'segy_spec': segy_spec,
                'template_path': path,
                'unstructured': True
            }
            
            return np.ascontiguousarray(seismic_data), metadata
            
    except Exception as e:
        raise IOError(f"Failed to load SEGY file even as unstructured data: {e}")

def _load_from_numpy(path):
    """Helper function to load data from a NumPy .npy file."""
    print(f"Loading NumPy array from: {path}")
    seismic_data = np.load(path)
    
    # Infer geometry from dimensions
    if seismic_data.ndim == 3:
        geometry = '3D'
    elif seismic_data.ndim == 2:
        geometry = '2D'
    else:
        geometry = 'Unknown'

    print(f"Successfully loaded NumPy array with shape {seismic_data.shape}")
    print("WARNING: Sample rate (dt) and time axis are unknown for .npy files.")

    metadata = {
        'type': 'numpy',
        'geometry': geometry,
        'shape': seismic_data.shape,
        'dt': None,
        'time_axis': None,
        'segy_spec': None,
        'template_path': None # Or path, if it could be useful for context
    }
    return seismic_data, metadata

# Example of how to use this function
if __name__ == '__main__':
    # This block will only run if you execute this script directly
    # You would need to create these dummy files or point to real ones.
    
    # --- Example 1: Create and load a dummy NumPy array ---
    print("\n--- TESTING NUMPY LOADER ---")
    dummy_3d_np = np.random.rand(50, 100, 250).astype(np.float32) # (IL, XL, T)
    np.save('dummy_3d.npy', dummy_3d_np)
    
    data, meta = load_seismic_data('dummy_3d.npy')
    print("Loaded data shape:", data.shape)
    print("Metadata:", meta)
    os.remove('dummy_3d.npy')

    # --- Example 2: How you would load a SEGY file ---
    # You need to provide your own SEGY file path here.
    # segy_path_3d = 'path/to/your/3d_file.sgy'
    # segy_path_2d = 'path/to/your/2d_file.sgy'
    
    # print("\n--- TESTING SEGY LOADER (EXAMPLE) ---")
    # try:
    #     data_3d, meta_3d = load_seismic_data(segy_path_3d)
    #     print("\nLoaded 3D SEGY data shape:", data_3d.shape)
    #     print("3D Metadata:", meta_3d)
    # except (FileNotFoundError, IOError) as e:
    #     print(f"Could not test 3D SEGY loading: {e}")

    # try:
    #     data_2d, meta_2d = load_seismic_data(segy_path_2d)
    #     print("\nLoaded 2D SEGY data shape:", data_2d.shape)
    #     print("2D Metadata:", meta_2d)
    # except (FileNotFoundError, IOError) as e:
    #     print(f"Could not test 2D SEGY loading: {e}")
    
    

def save(out_data, out_file):
    """
    Description
    -----------
    Convience function to read file and create a pointer to data on disk
    
    Parameters
    ----------
    out_data : Dask Array, data to be saved to disk
    out_file : str, path to file to save to
    """
    
    # Save to disk if object is Dask Array
    try:       
        out_data.to_hdf5(out_file, 'data')
    except Exception:
        raise Exception('Object is not a Dask Array')
        
        

def convert_dtype(in_data, min_val, max_val, to_dtype):
    """
    Description
    -----------
    Convience function to read file and create a pointer to data on disk
    
    Parameters
    ----------
    in_data : Dask Array, data to convert
    min_val : number, lower clip
    max_val : number, upper clip
    to_dtype : NumPy dtype
        Acceptable formats include (np.int8, np.float16, np.float32)
       
    Returns
    -------
    out : Dask Array, converted data
    """
    
    # Check if data is already in correct format
    if in_data.dtype == to_dtype:
        return(in_data)
        
    
    else:              
        
        in_data = da.clip(in_data, min_val, max_val)        
        if to_dtype == np.int8:
            in_data = ((in_data - min_val) / (max_val - min_val))
            dtype = np.iinfo(np.int8)
            in_data *= dtype.max - dtype.min
            in_data -= dtype.min
            out = in_data.astype(np.int8)
        
        elif to_dtype == np.float16:
            out = in_data.astype(np.float16)
            
        elif to_dtype == np.int32:
            out = in_data.astype(np.float32)
            
        else:
            raise Exception('Not a valid dtype')
    
    return(out)
    
    
    
def extract_patches(in_data, kernel):
    """
    Description
    -----------
    Reshape in_data into a collection of patches defined by kernel
    
    Parameters
    ----------
    in_data : Dask Array, data to convert
    kernel : tuple (len 3), operator size
       
    Returns
    -------
    out : Numpy Array, has shape (in_data.shape[0], in_data.shape[1], 
                                  in_data.shape[2], kernel[0], kernel[1], kernel[2])
    """
    
    strides = in_data.strides + in_data.strides
    shape = (np.array(in_data.shape) - np.array(kernel)) + 1
    shape = tuple(list(shape) + list(kernel))
    
    patches = np.lib.stride_tricks.as_strided(in_data,
                                              shape=shape,
                                              strides=strides)        
    return(patches)
    
    

def local_events(in_data, comparator):
    """
    Description
    -----------
    Find local peaks or troughs depending on comparator used
    
    Parameters
    ----------
    in_data : Dask Array, data to convert
    comparator : function, defines truth between neighboring elements
       
    Returns
    -------
    out : Numpy Array
    """
    
    idx = np.arange(0, in_data.shape[-1])    
    trace = in_data.take(idx, axis=-1, mode='clip')
    plus = in_data.take(idx + 1, axis=-1, mode='clip')
    minus = in_data.take(idx - 1, axis=-1, mode='clip')
    
    result = np.ones(in_data.shape, dtype=np.bool)
    
    result &= comparator(trace, plus)
    result &= comparator(trace, minus)
    
    return(result)
    
    
    
def hilbert(in_data):
    """
    Description
    -----------
    Perform Hilbert Transform on input data
    
    Parameters
    ----------
    in_data : Dask Array, data to convert
           
    Returns
    -------
    out : Numpy Array
    """
    
    N = in_data.shape[-1]
    
    Xf = np.fft.fftpack.fft(in_data, n=N, axis=-1)
    
    h = np.zeros(N)
    if N % 2 == 0:
        h[0] = h[N // 2] = 1
        h[1:N // 2] = 2
    else:
        h[0] = 1
        h[1:(N + 1) // 2] = 2

    if in_data.ndim > 1:
        ind = [np.newaxis] * in_data.ndim
        ind[-1] = slice(None)
        h = h[ind]
    x = np.fft.fftpack.ifft(Xf * h, axis=-1)
    return x