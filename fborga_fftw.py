# -*- coding: utf-8 -*-
"""
Created on Sun Jul  7 03:02:45 2024

@author: devri.agustian<PERSON>
Modified to use pyfftw
"""

import numpy as np
from scipy import signal as sig
import pyfftw
import multiprocessing

# Global variable initialization
XNOM = None
GAUS = None
DX = None
XMIN = None
XMAX = None
XWID = None

# Configure pyfftw to use all CPU cores
pyfftw.config.NUM_THREADS = multiprocessing.cpu_count()
# pyfftw.config.NUM_THREADS = pyfftw.config.CPU_COUNT

def nextpow2(x):
    return int(np.ceil(np.log2(np.abs(x))))

def fftrl(signal, t, pad_to=0):
    dt = t[1] - t[0]
    nt = len(signal)
    if pad_to == 0:
        pad_to = nt
    nf = pad_to // 2 + 1
    f = np.fft.rfftfreq(pad_to, dt)
    
    # Create pyfftw objects
    a = pyfftw.empty_aligned(pad_to, dtype='float64')
    b = pyfftw.empty_aligned(nf, dtype='complex128')
    
    fft_object = pyfftw.FFTW(a, b, flags=['FFTW_ESTIMATE'], threads=pyfftw.config.NUM_THREADS)
    
    a[:nt] = signal
    a[nt:] = 0  # Zero-padding
    
    spectrum = fft_object()
    return spectrum, f

def ifftrl(spectrum, f):
    nf = len(f)
    nt = 2 * (nf - 1)
    
    # Create pyfftw objects
    a = pyfftw.empty_aligned(nf, dtype='complex128')
    b = pyfftw.empty_aligned(nt, dtype='float64')
    
    ifft_object = pyfftw.FFTW(a, b, direction='FFTW_BACKWARD', flags=['FFTW_ESTIMATE'], threads=pyfftw.config.NUM_THREADS)
    
    a[:] = spectrum
    
    signal = ifft_object()
    return signal

def gaussian_upou(x, xnot, xwid, xinc, norm_factor=None, xnotvec=None, gdb=60, pow2option=1):
    global XNOM, GAUS, DX, XMIN, XMAX, XWID
    
    x = np.atleast_1d(x).flatten()
    
    dx = x[1] - x[0]
    xmin = x.min()
    xmax = x.max()
    nx = len(x)
    if pow2option:
        nx = 2**nextpow2(nx)
    if nx > len(x):
        x = np.arange(nx) * dx + xmin
        xmax = x.max()
    
    makeit = False
    if GAUS is None:
        makeit = True
    elif dx != DX or xmin != XMIN or xmax != XMAX or xwid != XWID or nx != len(GAUS):
        makeit = True
    
    if makeit:
        DX = dx
        XMIN = xmin
        XMAX = xmax
        XWID = xwid
        XNOM = np.arange(xmin-xmax, xmax-xmin+dx, dx)
        GAUS = np.exp(-(XNOM/xwid)**2)
    
    nwin = round((xmax - xmin) / xinc) + 1
    xinc = (xmax - xmin) / (nwin - 1)
    
    Xdb = xwid * np.sqrt(gdb / (20 * np.log10(np.e)))
    if np.isinf(Xdb):
        Xdb = (xmax - xmin) / 2
    nXdb = round(Xdb / dx)
    nwinstandard = 2 * nXdb
    
    if pow2option:
        nwinstandard = 2**nextpow2(nwinstandard)
        nXdb = nwinstandard // 2
        Xdb = dx * nXdb
    
    if nwinstandard > nx:
        nwinstandard = nx
        nXdb = nwinstandard // 2
        Xdb = dx * nXdb
    
    if nwinstandard == nx - 1:
        nwinstandard = nx
    
    if norm_factor is None or len(norm_factor) != nx or np.sum(np.abs(norm_factor)) == 0:
        norm_factor = np.zeros_like(x)
        x0 = xmin
        xnotvec = np.zeros(nwin)
        for k in range(nwin):
            xnotvec[k] = x0
            gwinnom = get_gaussian(x, x0)
            igoff = np.abs((x - x0) / dx)
            iuse2 = np.where(igoff <= nXdb)[0]
            iuse = make_standard_size(iuse2, nwinstandard, nx)
            gwin = gwinnom[iuse]
            norm_factor[iuse] += gwin
            x0 = x0 + xinc
            x0 = dx * round((x0 - xmin) / dx) + xmin
            if k + 1 == nwin and x0 > xmax:
                x0 = xmax
    
    ind = np.argmin(np.abs(xnotvec - xnot))
    xnot = xnotvec[ind]
    
    gwinnom = get_gaussian(x, xnot)
    igoff = np.abs((x - xnot) / dx)
    iuse2 = np.where(igoff <= nXdb)[0]
    iuse = make_standard_size(iuse2, nwinstandard, nx)
    gwin = gwinnom[iuse]
    gwin = gwin / norm_factor[iuse]
    i1 = iuse[0]
    
    return gwin, norm_factor, xnotvec, nwin, i1

def make_standard_size(iuse2, nwinstandard, nx):
    if len(iuse2) < nwinstandard:
        if iuse2[0] == 0:
            nu = len(iuse2)
            iuse = np.concatenate((iuse2, np.arange(iuse2[-1]+1, iuse2[-1]+nwinstandard-nu+1)))
        elif iuse2[-1] == nx - 1:
            nu = len(iuse2)
            iuse = np.concatenate((np.arange(iuse2[0]-nwinstandard+nu, iuse2[0]), iuse2))
        else:
            raise ValueError('Total logic failure in gaussian_upou')
    elif len(iuse2) == nwinstandard + 1:
        iuse = iuse2[:-1]
    else:
        iuse = iuse2
    
    if len(iuse) != nwinstandard:
        raise ValueError('Failed to create standard size window')
    
    return iuse

def get_gaussian(x, x0):
    global GAUS, XMAX, XMIN, DX
    xnom1 = x[0] - x0
    inom1 = round((xnom1 + XMAX - XMIN) / DX)
    inom = np.arange(inom1, inom1 + len(x))
    return GAUS[inom]

def fborga(signal, t, fwidth, finc, padflag=1):
    nt = len(signal)
    original_length = nt  # Store the original length
    if padflag:
        nt = 2**nextpow2(nt)
    
    spectrum, f = fftrl(signal, t, nt)
    fmin = f[0]
    
    ls = len(spectrum)
    iuse = np.arange(ls)
    if ls % 2 != 0:
        spectrum = np.pad(spectrum, (0, 1), mode='constant')
        f = np.pad(f, (0, 1), mode='constant', constant_values=(f[-1] + f[1] - f[0],))
        iuse = np.arange(ls + 1)
    
    gdb = np.inf
    g, norm_factor, fnotvec, nwin, _ = gaussian_upou(f, fmin, fwidth, finc, None, None, gdb, 1)
    
    # Initialize tvs with the correct size
    tvs = np.zeros((original_length, nwin))
    fout = np.zeros(nwin)
    
    for k in range(nwin):
        fnow = fnotvec[k]
        fout[k] = fnow
        g, _, _, _, _ = gaussian_upou(f, fnow, fwidth, finc, norm_factor, fnotvec, gdb, 1)
        
        # Ensure g has the same length as spectrum
        if len(g) > len(spectrum):
            g = g[:len(spectrum)]
        elif len(g) < len(spectrum):
            g = np.pad(g, (0, len(spectrum) - len(g)), mode='constant')
        
        S = spectrum[iuse] * g
        ifft_result = ifftrl(S, f[iuse])
        
        # Scale or stretch/squeeze ifft_result to match original_length
        if len(ifft_result) != original_length:
            tvs[:, k] = sig.resample(ifft_result, original_length)
        else:
            tvs[:, k] = ifft_result
    
    return tvs, fout, t