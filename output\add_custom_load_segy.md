Here is a standalone function, `load_seismic_data`, that can be added to your `util.py` file or kept in its own new `data_loader.py` file. It is designed to:

1.  Accept a file path for either a SEGY (`.sgy`/`.segy`) or a NumPy (`.npy`) file.
2.  Intelligently detect the geometry of the SEGY file (3D, 2D, or single/arbitrary trace).
3.  Load the data into a NumPy array with a consistent axis convention.
4.  Return both the data array and a dictionary of useful metadata (like sample rate `dt`, time axis, geometry type, etc.).

---

### New/Updated File: `util.py` (or a new `data_loader.py`)

You can add this function to your existing `util.py` file.

```python
# --- util.py ---
# ... (all existing functions like compute_chunk_size, trim_dask_array, etc.) ...

# NEW ROBUST SEISMIC LOADER FUNCTION
import os
import segyio
import numpy as np

def load_seismic_data(path):
    """
    Robustly loads seismic data from SEGY or NumPy files.

    This function intelligently detects the data format and geometry (3D, 2D, etc.)
    and returns a NumPy array with consistent axis ordering, along with essential metadata.

    Axis Convention:
    - 3D Data: (inline, xline, time_sample)
    - 2D Data: (trace, time_sample)

    Parameters
    ----------
    path : str
        The full path to the input file. Supported extensions: .sgy, .segy, .npy.

    Returns
    -------
    seismic_data : numpy.ndarray
        The loaded seismic data. Shape will be 3D or 2D depending on the source.
    metadata : dict
        A dictionary containing key information about the data:
        - 'type' (str): 'segy' or 'numpy'.
        - 'geometry' (str): '3D', '2D', or 'Unknown'.
        - 'shape' (tuple): The shape of the returned `seismic_data` array.
        - 'dt' (float): The sample interval in seconds (e.g., 0.002). Is None for .npy.
        - 'time_axis' (np.array): The time axis in seconds. Is None for .npy.
        - 'segy_spec' (segyio.spec): The segyio spec object, useful for writing output.
                                     Is None for .npy.
        - 'template_path' (str): The original path, to be used as a template for writing.

    Raises
    ------
    FileNotFoundError
        If the specified path does not exist.
    ValueError
        If the file type is not supported.
    IOError
        If there is an issue reading the SEGY file.
    """
    if not os.path.exists(path):
        raise FileNotFoundError(f"The specified file does not exist: {path}")

    _, ext = os.path.splitext(path)
    ext = ext.lower()

    if ext in ['.sgy', '.segy']:
        return _load_from_segy(path)
    elif ext == '.npy':
        return _load_from_numpy(path)
    else:
        raise ValueError(f"Unsupported file type: '{ext}'. Please use .sgy, .segy, or .npy.")

def _load_from_segy(path):
    """Helper function to load data from a SEGY file."""
    print(f"Loading SEGY file: {path}")
    try:
        # Use strict=False to be more lenient with non-standard files
        with segyio.open(path, strict=False) as f:
            # --- 1. Extract Basic Metadata ---
            segy_spec = f.spec
            dt = segy_spec.format.sample_interval / 1_000_000.0 if segy_spec.format.sample_interval > 0 else 0.002
            time_axis = f.samples / 1000.0 if f.samples is not None and len(f.samples) > 0 else np.arange(segy_spec.samples) * dt
            
            # --- 2. Detect Geometry ---
            is_3d = f.iline.size > 1 and f.xline.size > 1

            if is_3d:
                print("Detected 3D geometry. Loading as a cube...")
                geometry = '3D'
                # segyio.tools.cube is the most robust way to load a 3D volume
                seismic_data = segyio.tools.cube(path)
                
                # Ensure (IL, XL, T) dimension order
                iline_dim, xline_dim = f.iline.size, f.xline.size
                if seismic_data.shape == (xline_dim, iline_dim, f.samples.size):
                    print("Data was (XLINE, ILINE, TIME), transposing to (ILINE, XLINE, TIME).")
                    seismic_data = np.transpose(seismic_data, (1, 0, 2))
                
            else: # Handle 2D, arbitrary lines, or single traces
                print("Could not detect standard 3D geometry. Loading as 2D/arbitrary line(s).")
                geometry = '2D'
                # f.trace.raw loads all traces into a (trace_count, sample_count) array
                seismic_data = f.trace.raw[:]
                
        print(f"Successfully loaded {geometry} data with shape {seismic_data.shape}")
        
        metadata = {
            'type': 'segy',
            'geometry': geometry,
            'shape': seismic_data.shape,
            'dt': dt,
            'time_axis': time_axis,
            'segy_spec': segy_spec,
            'template_path': path
        }
        return np.ascontiguousarray(seismic_data), metadata

    except Exception as e:
        raise IOError(f"Failed to read SEGY file '{path}'. Error: {e}")

def _load_from_numpy(path):
    """Helper function to load data from a NumPy .npy file."""
    print(f"Loading NumPy array from: {path}")
    seismic_data = np.load(path)
    
    # Infer geometry from dimensions
    if seismic_data.ndim == 3:
        geometry = '3D'
    elif seismic_data.ndim == 2:
        geometry = '2D'
    else:
        geometry = 'Unknown'

    print(f"Successfully loaded NumPy array with shape {seismic_data.shape}")
    print("WARNING: Sample rate (dt) and time axis are unknown for .npy files.")

    metadata = {
        'type': 'numpy',
        'geometry': geometry,
        'shape': seismic_data.shape,
        'dt': None,
        'time_axis': None,
        'segy_spec': None,
        'template_path': None
    }
    return seismic_data, metadata

# Example of how to use this function
if __name__ == '__main__':
    # This block will only run if you execute this script directly
    # You would need to create these dummy files or point to real ones.
    
    # --- Example 1: Create and load a dummy NumPy array ---
    print("\n--- TESTING NUMPY LOADER ---")
    dummy_3d_np = np.random.rand(50, 100, 250).astype(np.float32) # (IL, XL, T)
    np.save('dummy_3d.npy', dummy_3d_np)
    
    data, meta = load_seismic_data('dummy_3d.npy')
    print("Loaded data shape:", data.shape)
    print("Metadata:", meta)
    os.remove('dummy_3d.npy')

    # --- Example 2: How you would load a SEGY file ---
    # You need to provide your own SEGY file path here.
    # segy_path_3d = 'path/to/your/3d_file.sgy'
    # segy_path_2d = 'path/to/your/2d_file.sgy'
    
    # print("\n--- TESTING SEGY LOADER (EXAMPLE) ---")
    # try:
    #     data_3d, meta_3d = load_seismic_data(segy_path_3d)
    #     print("\nLoaded 3D SEGY data shape:", data_3d.shape)
    #     print("3D Metadata:", meta_3d)
    # except (FileNotFoundError, IOError) as e:
    #     print(f"Could not test 3D SEGY loading: {e}")

    # try:
    #     data_2d, meta_2d = load_seismic_data(segy_path_2d)
    #     print("\nLoaded 2D SEGY data shape:", data_2d.shape)
    #     print("2D Metadata:", meta_2d)
    # except (FileNotFoundError, IOError) as e:
    #     print(f"Could not test 2D SEGY loading: {e}")

```

### How to Integrate and Use It

1.  **Place the Code:** Add the functions `load_seismic_data`, `_load_from_segy`, and `_load_from_numpy` to your `util.py` file.
2.  **Import It:** In your main script (`6_Run_multispectral_coherence.py.py`), make sure you import it:
    ```python
    # In 6_Run_multispectral_coherence.py.py
    import util # Or from util import load_seismic_data
    ```
3.  **Update the Main Script:** Replace the old loading block in `main()` with a simple call to this new function. The script becomes much cleaner.

#### Modified `main` function in `6_Run_multispectral_coherence.py.py`

```python
# File: 6_Run_multispectral_coherence.py.py

# ... (imports) ...
# Make sure to import the new function
import util

# ... (get_user_inputs function remains the same) ...

def main():
    """
    Main workflow for computing multispectral coherence.
    """
    # --- 1. GET USER INPUTS ---
    params = get_user_inputs()
    if not params:
        print("Process cancelled.")
        return

    # --- 2. LOAD SEISMIC DATA using the new robust loader ---
    try:
        seismic_data_il_xl_t, meta = util.load_seismic_data(params["input_segy_path"])
    except (FileNotFoundError, ValueError, IOError) as e:
        print(f"\n--- ERROR ---")
        print(f"Could not load the seismic data. Reason: {e}")
        return

    # Check if data is 3D, as required by the rest of the script
    if meta['geometry'] != '3D':
        print(f"Error: The multispectral coherence script requires a 3D input volume, but loaded data is '{meta['geometry']}'.")
        return

    # Extract necessary metadata
    dt = meta['dt']
    time_axis = meta['time_axis']
    template_segy_path = meta['template_path']
    
    print(f"Data loaded successfully. Shape (IL, XL, T): {meta['shape']}, dt: {dt:.4f}s")

    # --- 3. GENERATE SPECTRAL VOICES using fborga_3d ---
    print(f"Generating spectral voices for target frequencies: {params['target_frequencies']} Hz...")
    start_time = time.time()
    
    # fborga_3d expects (time, inline, xline). We must transpose.
    seismic_t_il_xl = np.transpose(seismic_data_il_xl_t, (2, 0, 1))
    
    # Run the Borga transform
    tvs_4d, fout, _ = fborga_3d(
        seismic_volume=seismic_t_il_xl,
        t=time_axis,
        fwidth=params['fwidth'],
        finc=params['finc'],
        target_freqs=params['target_frequencies']
    )
    # ... (the rest of the script continues exactly as before) ...
```

This approach significantly improves your project's structure. The main script now focuses on the *workflow* (load -> process -> save -> plot), while the complex details of *how* to load data are neatly encapsulated in a reusable, robust, and easy-to-understand utility function.