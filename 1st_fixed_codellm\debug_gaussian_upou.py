#!/usr/bin/env python3
"""
Debug script to understand gaussian_upou frequency calculation.
"""

import numpy as np
from scipy.fft import rfftfreq
from fborga_2d_3d import nextpow2, fftrl, gaussian_upou

# Test parameters
nt = 1024
dt = 0.002
t = np.arange(nt) * dt
signal = np.random.randn(nt)
fwidth = 10.0
finc = 2.0

# Get the frequency array
n_padded = 2**nextpow2(nt)
spectrum, f = fftrl(signal, t, n_padded)

print(f"Input frequency array:")
print(f"  Length: {len(f)}")
print(f"  Range: {f[0]} to {f[-1]} Hz")
print(f"  dx (increment): {f[1] - f[0]}")

# Call gaussian_upou with debug info
fmin = f[0]
print(f"\nCalling gaussian_upou with:")
print(f"  x (frequency array): length {len(f)}, range {f[0]} to {f[-1]}")
print(f"  xnot (fmin): {fmin}")
print(f"  xwid (fwidth): {fwidth}")
print(f"  xinc (finc): {finc}")

# Let's manually calculate what gaussian_upou should do
x = f
dx = x[1] - x[0]
xmin = x.min()
xmax = x.max()
nx = len(x)

print(f"\nInside gaussian_upou calculation:")
print(f"  dx: {dx}")
print(f"  xmin: {xmin}")
print(f"  xmax: {xmax}")
print(f"  nx: {nx}")

# Calculate nwin
nwin = round((xmax - xmin) / finc) + 1
xinc_adjusted = (xmax - xmin) / (nwin - 1)

print(f"  nwin: {nwin}")
print(f"  xinc_adjusted: {xinc_adjusted}")

# Generate the frequency grid like gaussian_upou does
x0 = xmin
xnotvec = np.zeros(nwin)
for k in range(nwin):
    xnotvec[k] = x0
    x0 += xinc_adjusted
    x0 = dx * round((x0 - xmin) / dx) + xmin
    if k + 1 == nwin and x0 > xmax:
        x0 = xmax

print(f"\nGenerated frequency grid:")
print(f"  First 10: {xnotvec[:10]}")
print(f"  Last 10: {xnotvec[-10:]}")
print(f"  Max frequency: {xnotvec.max()}")

# Now call the actual function
_, norm_factor, fnotvec_actual, nwin_actual, _ = gaussian_upou(f, fmin, fwidth, finc, gdb=np.inf)

print(f"\nActual gaussian_upou output:")
print(f"  nwin: {nwin_actual}")
print(f"  First 10: {fnotvec_actual[:10]}")
print(f"  Last 10: {fnotvec_actual[-10:]}")
print(f"  Max frequency: {fnotvec_actual.max()}")

print(f"\nComparison:")
print(f"  Manual vs actual match: {np.allclose(xnotvec, fnotvec_actual)}")