#!/usr/bin/env python3
"""
Debug script to understand the pow2option effect in gaussian_upou.
"""

import numpy as np
from scipy.fft import rfftfreq
from fborga_2d_3d import nextpow2, fftrl

# Test parameters
nt = 1024
dt = 0.002
t = np.arange(nt) * dt
signal = np.random.randn(nt)

# Get the frequency array
n_padded = 2**nextpow2(nt)
spectrum, f = fftrl(signal, t, n_padded)

print(f"Original frequency array:")
print(f"  Length: {len(f)}")
print(f"  Range: {f[0]} to {f[-1]} Hz")

# Simulate what gaussian_upou does with pow2option=1
x = f
dx = x[1] - x[0]
xmin = x.min()
xmax = x.max()
nx = len(x)

print(f"\nInitial values:")
print(f"  nx: {nx}")
print(f"  xmin: {xmin}")
print(f"  xmax: {xmax}")

# Apply pow2option logic
pow2option = 1
if pow2option:
    nx = 2**nextpow2(nx)
    print(f"  nx after pow2option: {nx}")

if nx > len(x):
    x_padded = np.arange(nx) * dx + xmin
    xmax_new = x_padded.max()
    print(f"  Extended frequency array:")
    print(f"    New length: {nx}")
    print(f"    New xmax: {xmax_new}")
    print(f"    New range: {xmin} to {xmax_new}")
    
    # Calculate nwin with the new xmax
    finc = 2.0
    nwin = round((xmax_new - xmin) / finc) + 1
    print(f"    nwin with new range: {nwin}")
else:
    print(f"  No extension needed")
    nwin = round((xmax - xmin) / finc) + 1
    print(f"    nwin with original range: {nwin}")