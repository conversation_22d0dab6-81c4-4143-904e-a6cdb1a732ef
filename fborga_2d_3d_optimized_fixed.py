"""
fborga_2d_3d_optimized.py

Optimized Borga transform implementation with NumPy/CuPy support.
Supports both FFT and convolution algorithms with GPU acceleration.

This implementation fixes the output shape to match the original:
- Output shape: (nt, nwin, ntr) for 2D input
- Output shape: (nt, nwin, n1, n2) for 3D input
"""

import numpy as np
from typing import Optional, Sequence
from scipy.fft import rfft, irfft, rfftfreq

# Try to import CuPy for GPU acceleration
try:
    import cupy as cp
    import cupyx.scipy.fft as cp_fft
    HAS_CUPY = True
except ImportError:
    HAS_CUPY = False

__all__ = [
    "fborga",
    "fborga_2d", 
    "fborga_3d"
]

def _xp(arr=None, backend: str = "auto"):
    """Choose array library (NumPy or CuPy) based on backend preference."""
    if backend == "numpy":
        return np
    elif backend == "cupy":
        if not HAS_CUPY:
            raise ImportError("CuPy not available")
        return cp
    elif backend == "auto":
        if HAS_CUPY and arr is not None and hasattr(arr, "__array_interface__"):
            # Check if array is already on GPU
            try:
                cp.asarray(arr)
                return cp
            except:
                return np
        elif HAS_CUPY:
            return cp
        else:
            return np
    else:
        raise ValueError("backend must be 'auto', 'numpy', or 'cupy'")

def _gauss_stack(freqs, fwidth: float, finc: float, xp, target_freqs: Optional[Sequence[float]] = None):
    """Return stack of Gaussian windows *g* and their centre frequencies."""

    # Mimic the original gaussian_upou frequency extension logic
    dx = float(freqs[1] - freqs[0])
    xmin = float(freqs[0])
    xmax = float(freqs[-1])
    nx = len(freqs)
    
    # Apply pow2option logic like the original
    nx_extended = 2**int(np.ceil(np.log2(nx)))
    if nx_extended > nx:
        xmax_extended = xmin + (nx_extended - 1) * dx
    else:
        xmax_extended = xmax
    
    # Generate frequency grid exactly like gaussian_upou
    nwin = round((xmax_extended - xmin) / finc) + 1
    xinc_adjusted = (xmax_extended - xmin) / (nwin - 1) if nwin > 1 else finc
    
    # Generate frequencies with quantization like the original
    x0 = xmin
    centres_list = []
    for k in range(nwin):
        centres_list.append(x0)
        x0 += xinc_adjusted
        # Quantize to the original frequency grid
        x0 = dx * round((x0 - xmin) / dx) + xmin
        if k + 1 == nwin and x0 > xmax_extended:
            x0 = xmax_extended
    
    centres_full = np.array(centres_list)
    
    if target_freqs is None:
        centres = xp.asarray(centres_full, dtype=xp.float32)
    else:
        # For target frequencies, find the closest frequencies in the full grid
        target_freqs = np.atleast_1d(target_freqs)
        indices = np.array([np.argmin(np.abs(centres_full - f_target)) for f_target in target_freqs])
        centres = xp.asarray(centres_full[indices], dtype=xp.float32)
    
    g = xp.exp(-((freqs[:, None] - centres[None, :]) / fwidth) ** 2)
    
    # Avoid division by zero in normalization
    g_sum = xp.sum(g ** 2, axis=0, keepdims=True)
    g_sum = xp.where(g_sum == 0, 1.0, g_sum)  # Replace zeros with 1 to avoid division by zero
    g /= g_sum  # energy normalisation
    
    return g.astype(xp.float32), centres


# -----------------------------------------------------------------------------
# Core – FFT‑domain Borga (shared forward FFT, batched inverse FFT)
# -----------------------------------------------------------------------------

def _borga_fft(data, dt: float, fwidth: float, finc: float, xp, target_freqs=None, padflag: bool = True):
    """FFT‑domain Borga transform."""
    nt = data.shape[0]
    nfft = int(2 ** np.ceil(np.log2(nt))) if padflag else nt

    if xp is np:
        spec = rfft(data, n=nfft, axis=0)
        freqs = rfftfreq(nfft, dt)
    else:
        spec = cp_fft.rfft(data, n=nfft, axis=0)  # type: ignore[attr-defined]
        freqs = cp_fft.rfftfreq(nfft, dt)  # type: ignore[attr-defined]

    g, centres = _gauss_stack(freqs, fwidth, finc, xp, target_freqs)

    # Broadcast multiply to get proper output shape (nt, nwin, ...)
    # spec shape: (nf, ...), g shape: (nf, nwin)
    # We want output shape: (nt, nwin, ...)
    if data.ndim == 1:
        # For 1D data: spec (nf,), g (nf, nwin) -> result (nt, nwin)
        tvs = xp.fft.irfft(spec[:, None] * g, n=nfft, axis=0)[:nt]
    else:
        # For multi-D data: spec (nf, ...), g (nf, nwin) 
        # Need to reshape to get (nt, nwin, ...) output
        # spec[:, None, ...] gives (nf, 1, ...), g[..., None] gives (nf, nwin, 1)
        spec_expanded = spec[:, None, ...]  # (nf, 1, ...)
        g_expanded = g[..., None]  # (nf, nwin, 1)
        # After multiplication: (nf, nwin, ...), after irfft: (nt, nwin, ...)
        tvs = xp.fft.irfft(spec_expanded * g_expanded, n=nfft, axis=0)[:nt]
    return tvs, centres if xp is np else xp.asnumpy(centres)


# -----------------------------------------------------------------------------
# Core – time‑domain convolution Borga (for short kernels)
# -----------------------------------------------------------------------------

def _gauss_kernel(nt: int, dt: float, f0: float, fwidth: float, xp, padflag: bool = True):
    """Return zero‑phase Gaussian kernel in *time* domain centred at *f0*."""
    nfft = int(2 ** np.ceil(np.log2(nt))) if padflag else nt
    if xp is np:
        freqs = rfftfreq(nfft, dt)
        win = np.exp(-((freqs - f0) / fwidth) ** 2)
        win /= np.sum(win ** 2)
        kernel = irfft(win, n=nfft)
    else:
        freqs = cp_fft.rfftfreq(nfft, dt)  # type: ignore[attr-defined]
        win = xp.exp(-((freqs - f0) / fwidth) ** 2)
        win /= xp.sum(win ** 2)
        kernel = xp.fft.irfft(win, n=nfft)
    return kernel[:nt]


def _borga_conv(data, dt: float, fwidth: float, finc: float, xp, target_freqs=None, padflag: bool = True):
    nt = data.shape[0]
    fnyq = 1.0 / (2.0 * dt)
    
    # Mimic the original gaussian_upou frequency extension logic
    # Create a frequency array like the FFT version would
    nfft = int(2 ** np.ceil(np.log2(nt))) if padflag else nt
    freqs_fft = np.linspace(0, fnyq, nfft // 2 + 1)
    
    dx = float(freqs_fft[1] - freqs_fft[0])
    xmin = 0.0
    xmax = fnyq
    nx = len(freqs_fft)
    
    # Apply pow2option logic like the original
    nx_extended = 2**int(np.ceil(np.log2(nx)))
    if nx_extended > nx:
        xmax_extended = xmin + (nx_extended - 1) * dx
    else:
        xmax_extended = xmax
    
    # Generate frequency grid exactly like gaussian_upou
    nwin = round((xmax_extended - xmin) / finc) + 1
    xinc_adjusted = (xmax_extended - xmin) / (nwin - 1) if nwin > 1 else finc
    
    # Generate frequencies with quantization like the original
    x0 = xmin
    centres_list = []
    for k in range(nwin):
        centres_list.append(x0)
        x0 += xinc_adjusted
        # Quantize to the original frequency grid
        x0 = dx * round((x0 - xmin) / dx) + xmin
        if k + 1 == nwin and x0 > xmax_extended:
            x0 = xmax_extended
    
    centres_full = np.array(centres_list)
    
    if target_freqs is None:
        centres = xp.asarray(centres_full, dtype=xp.float32)
    else:
        # For target frequencies, find the closest frequencies in the full grid
        target_freqs = np.atleast_1d(target_freqs)
        indices = np.array([np.argmin(np.abs(centres_full - f_target)) for f_target in target_freqs])
        centres = xp.asarray(centres_full[indices], dtype=xp.float32)
    
    nwin = len(centres)

    tvs = xp.zeros((nt, nwin) + data.shape[1:], dtype=xp.float32)
    for i, f0 in enumerate(centres):
        kernel = _gauss_kernel(nt, dt, f0, fwidth, xp, padflag)
        if data.ndim == 1:
            tvs[:, i] = xp.convolve(data, kernel, mode="same")
        else:
            for j in range(data.shape[1]):
                if data.ndim == 2:
                    tvs[:, i, j] = xp.convolve(data[:, j], kernel, mode="same")
                elif data.ndim == 3:
                    for k in range(data.shape[2]):
                        tvs[:, i, j, k] = xp.convolve(data[:, j, k], kernel, mode="same")
    return tvs, centres if xp is np else xp.asnumpy(centres)


# -----------------------------------------------------------------------------
# Main API
# -----------------------------------------------------------------------------

def fborga(data, t: np.ndarray, *, fwidth: float, finc: float, algorithm: str = "fft", backend: str = "auto", padflag: bool = True, target_freqs: Optional[Sequence[float]] = None):
    """General Borga transform.

    Parameters
    ----------
    data : ndarray
        Array whose **first** dimension is time/depth. Additional dimensions are
        treated as independent traces.
    t : 1‑D array
        Sample axis (seconds or ms) – used only for *dt*.
    fwidth, finc : float
        Gaussian half‑width (Hz) and increment between windows (Hz).
    algorithm : {'fft', 'conv'}
        *fft* – classic frequency‑domain implementation (fast, recommended).
        *conv* – time‑domain convolution (for research / very short kernels).
    backend : {'auto', 'numpy', 'cupy'}
        Pick array library. ``'auto'`` chooses CuPy when available.
    padflag : bool, default True
        Zero‑pad each trace to next power‑of‑two for FFT speed.
    target_freqs : sequence, optional
        If provided, output only these centre frequencies.

    Returns
    -------
    tvs : ndarray (nt, nwin, ...)
        Borga slices.
    freqs : ndarray (nwin,)
        Centre frequencies (Hz).
    tout : ndarray (nt,)
        Truncated/padded time axis.
    """

    dt = float(t[1] - t[0])
    xp = _xp(data, backend)
    data_gpu = xp.asarray(data, dtype=xp.float32)

    if algorithm == "fft":
        tvs, freqs = _borga_fft(data_gpu, dt, fwidth, finc, xp, target_freqs, padflag)
    elif algorithm == "conv":
        tvs, freqs = _borga_conv(data_gpu, dt, fwidth, finc, xp, target_freqs, padflag)
    else:
        raise ValueError("algorithm must be 'fft' or 'conv'")

    return tvs, freqs, t[: data.shape[0]]


def fborga_2d(section, t, fwidth: float, finc: float, *, algorithm: str = "fft", backend: str = "auto", padflag: bool = True, target_freqs: Optional[Sequence[float]] = None):
    if section.ndim != 2:
        raise ValueError("section must be 2‑D (nt, ntr)")
    tvs, freqs, tout = fborga(section, t, fwidth=fwidth, finc=finc, algorithm=algorithm, backend=backend, padflag=padflag, target_freqs=target_freqs)
    # tvs shape: nt, nwin, ntr
    return tvs, freqs, tout


def fborga_3d(volume, t, fwidth: float, finc: float, *, algorithm: str = "fft", backend: str = "auto", padflag: bool = True, target_freqs: Optional[Sequence[float]] = None):
    if volume.ndim != 3:
        raise ValueError("volume must be 3‑D (nt, n1, n2)")
    nt, n1, n2 = volume.shape
    tvs_flat, freqs, tout = fborga(volume.reshape(nt, -1), t, fwidth=fwidth, finc=finc, algorithm=algorithm, backend=backend, padflag=padflag, target_freqs=target_freqs)
    tvs = tvs_flat.reshape(nt, len(freqs), n1, n2)
    return tvs, freqs, tout

# End of file.