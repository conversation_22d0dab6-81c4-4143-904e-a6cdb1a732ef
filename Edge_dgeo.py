# File: Edge_dgeo.py

# Import Libraries
import dask.array as da
import numpy as np
from scipy import ndimage as ndi
import util
from SignalProcess import SignalProcess as sp

class EdgeDetection():
    """
    Description
    -----------
    Class object containing methods for computing edge attributes 
    from 3D seismic data.
    """

    def create_array(self, darray, kernel, preview):
        # ... (This method remains unchanged) ...
        # Compute chunk size and convert if not a Dask Array
        if not isinstance(darray, da.core.Array):  
            chunk_size = util.compute_chunk_size(darray.shape, 
                                               darray.dtype.itemsize, 
                                               kernel=kernel,
                                               preview=preview)
            darray = da.from_array(darray, chunks=chunk_size)
            chunks_init = darray.chunks            
        else:
            chunks_init = darray.chunks
        # Ghost Dask Array if operation specifies a kernel
        if kernel != None:
                hw = np.array(kernel) // 2
                pad_width = tuple((d, d) for d in hw)
                darray = da.pad(darray, pad_width, mode='reflect')
        return(darray, chunks_init)

    def semblance(self, darray, kernel=(3,3,9), preview=None):
        # ... (This method remains unchanged) ...
        def operation(chunk, kernel):
            np.seterr(all='ignore')
            x = util.extract_patches(chunk, kernel)
            s1 = np.sum(x, axis=(-3,-2)) ** 2
            s2 = np.sum(x ** 2, axis=(-3,-2))
            sembl = s1.sum(axis = -1) / s2.sum(axis = -1)
            sembl /= kernel[0] * kernel[1]
            return(sembl)
        darray, chunks_init = self.create_array(darray, kernel, preview)                
        result = darray.map_blocks(operation, kernel=kernel, dtype=darray.dtype, chunks=chunks_init)
        result[da.isnan(result)] = 0 
        return(result)

    def eig_complex(self, darray, kernel=(3,3,9), preview=None):
        # ... (This method remains unchanged) ...
        def cov(x, ki, kj, kk):
            x = x.reshape((ki * kj, kk))
            x = np.hstack([x.real, x.imag])
            return(x.dot(x.T))
        def operation(chunk, kernel):
            np.seterr(all='ignore')
            ki, kj, kk = kernel
            patches = util.extract_patches(chunk, kernel)
            out_data = []
            for i in range(0, patches.shape[0]):
                traces = patches[i]
                traces = traces.reshape(-1, ki * kj * kk)
                cov_matrix = np.apply_along_axis(cov, 1, traces, ki, kj, kk)
                vals = np.linalg.eigvals(cov_matrix)
                vals = np.abs(vals.max(axis=1) / vals.sum(axis=1))
                out_data.append(vals)
            out_data = np.asarray(out_data).reshape(patches.shape[:3])            
            return(out_data)
        darray, chunks_init = self.create_array(darray, kernel, preview)        
        hilbert = darray.map_blocks(util.hilbert, dtype=darray.dtype)
        result = hilbert.map_blocks(operation, kernel=kernel, dtype=darray.dtype)
        result = util.trim_dask_array(result, kernel)
        result[da.isnan(result)] = 0
        return(result)

    # =========================================================================
    # --- NEW MULTISPECTRAL COHERENCE METHOD ---
    # =========================================================================
    def multispectral_eig(self, spectral_voices, kernel=(3,3,9), preview=None):
        """
        Description
        -----------
        Compute multi-trace MULTISPECTRAL eigenstructure coherence from a list
        of spectrally decomposed seismic volumes. This method sums the covariance
        matrices of each spectral voice before computing the final coherence,
        enhancing geologically significant features.
        
        Parameters
        ----------
        spectral_voices : list of Array-like. Each element is a 3D volume for one
                          spectral voice. All must have the same shape. Can be
                          real or complex-valued.
        
        Keyword Arguments
        -----------------  
        kernel : tuple (len 3), operator size for the analysis window.
        preview : str, enables or disables preview mode and specifies direction.
        
        Returns
        -------
        result : Dask Array
        """
        # --- 1. PREPARE DASK ARRAYS FOR ALL VOICES ---
        # First, ensure all input voices are Dask arrays with the same chunking
        input_dask_voices = []
        common_chunks = None

        for voice_data in spectral_voices:
            if not isinstance(voice_data, da.core.Array):
                # Convert to Dask array if needed
                chunk_size = util.compute_chunk_size(voice_data.shape,
                                                   voice_data.dtype.itemsize,
                                                   kernel=kernel,
                                                   preview=preview)
                voice_dask = da.from_array(voice_data, chunks=chunk_size)
            else:
                voice_dask = voice_data

            # Set common chunking from first voice
            if common_chunks is None:
                common_chunks = voice_dask.chunks
            else:
                # Rechunk to match the first voice if needed
                if voice_dask.chunks != common_chunks:
                    voice_dask = voice_dask.rechunk(common_chunks)

            input_dask_voices.append(voice_dask)

        # Now process all voices through create_array with consistent input chunking
        dask_voices = []
        chunks_init = None

        for voice_dask in input_dask_voices:
            dask_voice, chunks = self.create_array(voice_dask, kernel, preview)
            dask_voices.append(dask_voice)
            if chunks_init is None:
                chunks_init = chunks

        # --- 2. DEFINE THE CORE OPERATION TO APPLY TO EACH CHUNK ---
        def operation(*blocks, kernel):
            np.seterr(all='ignore')
            chunk_shape, (ki, kj, kk) = blocks[0].shape, kernel
            n_traces = ki * kj
            cov_multi = np.zeros((n_traces, n_traces), dtype=np.complex128)
            out_chunk = np.zeros(chunk_shape, dtype=np.float64)

            # Iterate over every central point (i,j,k) within the UN-GHOSTED chunk
            for i in range(chunk_shape[0] - ki + 1):
                for j in range(chunk_shape[1] - kj + 1):
                    for k in range(chunk_shape[2] - kk + 1):
                        cov_multi.fill(0)
                        
                        # Loop through the chunk from each spectral voice
                        for voice_chunk in blocks:
                            window = voice_chunk[i:i+ki, j:j+kj, k:k+kk]
                            d_voice = window.reshape(n_traces, kk)
                            cov_multi += d_voice.dot(d_voice.T.conj())

                        vals = np.linalg.eigvalsh(cov_multi)
                        coherence_val = vals.max().real / (vals.sum().real + 1e-12)
                        out_chunk[i + ki//2, j + kj//2, k + kk//2] = coherence_val
            
            return out_chunk

        # --- 3. MAP THE OPERATION ACROSS ALL DASK CHUNKS ---
        result = da.map_blocks(operation, *dask_voices, kernel=kernel, dtype=np.float64, chunks=chunks_init)
        result[da.isnan(result)] = 0
        return(result)

    def gradient_structure_tensor(self, darray, kernel=(3,3,9), preview=None):
        # ... (This and other methods below remain unchanged) ...
        def operation(gi2, gj2, gk2, gigj, gigk, gjgk):
            np.seterr(all='ignore')
            chunk_shape = gi2.shape
            gst = np.array([[gi2, gigj, gigk],
                          [gigj, gj2, gjgk],
                          [gigk, gjgk, gk2]])
            gst = np.moveaxis(gst, [0,1], [-2,-1])
            gst = gst.reshape((-1, 3, 3))
            eigs = np.sort(np.linalg.eigvalsh(gst))
            e1, e2, e3 = eigs[:, 2].reshape(chunk_shape), eigs[:, 1].reshape(chunk_shape), eigs[:, 0].reshape(chunk_shape)
            cline = (e2 - e3) / (e2 + e3)
            cplane = (e1 - e2) / (e1 + e2)
            cfault = cline * (1 - cplane)
            return(cfault)
        darray, chunks_init = self.create_array(darray, kernel, preview)            
        gi, gj, gk = sp().first_derivative(darray, axis=0), sp().first_derivative(darray, axis=1), sp().first_derivative(darray, axis=2)
        gi2, gj2, gk2 = (gi * gi).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype), (gj * gj).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype), (gk * gk).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype)
        gigj, gigk, gjgk = (gi * gj).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype), (gi * gk).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype), (gj * gk).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype)
        result = da.map_blocks(operation, gi2, gj2, gk2, gigj, gigk, gjgk, dtype=darray.dtype)    
        result = util.trim_dask_array(result, kernel)
        result[da.isnan(result)] = 0
        return(result)

    def chaos(self, darray, kernel=(3,3,9), preview=None):
        """
        Description
        -----------
        Compute multi-trace chaos from 3D seismic
        
        Parameters
        ----------
        darray : Array-like, acceptable inputs include Numpy, HDF5, or Dask Arrays
        
        Keywork Arguments
        -----------------  
        kernel : tuple (len 3), operator size
        preview : str, enables or disables preview mode and specifies direction
            Acceptable inputs are (None, 'inline', 'xline', 'z')
            Optimizes chunk size in different orientations to facilitate rapid
            screening of algorithm output
        
        Returns
        -------
        result : Dask Array
        """
        
        # Function to extract patches and perform algorithm
        def operation(gi2, gj2, gk2, gigj, gigk, gjgk):
            np.seterr(all='ignore')
            
            chunk_shape = gi2.shape
            
            gst = np.array([[gi2, gigj, gigk],
                          [gigj, gj2, gjgk],
                          [gigk, gjgk, gk2]])
            
            gst = np.moveaxis(gst, [0,1], [-2,-1])
            gst = gst.reshape((-1, 3, 3))
            
            eigs = np.sort(np.linalg.eigvalsh(gst))
            e1 = eigs[:, 2].reshape(chunk_shape)
            e2 = eigs[:, 1].reshape(chunk_shape)
            e3 = eigs[:, 0].reshape(chunk_shape)
                
            out = (2 * e2) / (e1 + e3)
            
            return(out)
        
        # Generate Dask Array as necessary 
        darray, chunks_init = self.create_array(darray, kernel, preview)           
        
        # Compute I, J, K gradients
        gi = sp().first_derivative(darray, axis=0)
        gj = sp().first_derivative(darray, axis=1)
        gk = sp().first_derivative(darray, axis=2)
        
        # Compute the Inner Product of the Gradients
        gi2 = (gi * gi).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype)
        gj2 = (gj * gj).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype)
        gk2 = (gk * gk).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype)
        gigj = (gi * gj).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype)
        gigk = (gi * gk).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype)
        gjgk = (gj * gk).map_blocks(ndi.uniform_filter, size=kernel, dtype=darray.dtype)
            
        result = da.map_blocks(operation, gi2, gj2, gk2, gigj, gigk, gjgk, 
                               dtype=darray.dtype)    
        result = util.trim_dask_array(result, kernel)
        result[da.isnan(result)] = 0
        
        return(result)
        
        
    def volume_curvature(self, darray_il, darray_xl, dip_factor=10, kernel=(3,3,3), 
                         preview=None):
        """
        Description
        -----------
        Compute volume curvature attributes from 3D seismic dips
        
        Parameters
        ----------
        darray_il : Array-like, Inline dip - acceptable inputs include 
            Numpy, HDF5, or Dask Arrays
        darray_xl : Array-like, Crossline dip - acceptable inputs include 
            Numpy, HDF5, or Dask Arrays
        
        Keywork Arguments
        -----------------  
        dip_factor : Number, scalar for dip values
        kernel : tuple (len 3), operator size
        preview : str, enables or disables preview mode and specifies direction
            Acceptable inputs are (None, 'inline', 'xline', 'z')
            Optimizes chunk size in different orientations to facilitate rapid
            screening of algorithm output
        
        Returns
        -------
        H, K, Kmax, Kmin, KMPos, KMNeg : Dask Array, {H : 'Mean Curvature', 
                                                      K : 'Gaussian Curvature',
                                                      Kmax : 'Max Curvature',
                                                      Kmin : 'Min Curvature',
                                                      KMPos : Most Positive Curvature,
                                                      KMNeg : Most Negative Curvature}
        """
        
        np.seterr(all='ignore')
        
        # Generate Dask Array as necessary
        darray_il, chunks_init = self.create_array(darray_il, kernel, preview=preview)
        darray_xl, chunks_init = self.create_array(darray_xl, kernel, preview=preview)
        
        u = -darray_il / dip_factor
        v = -darray_xl / dip_factor
        w = da.ones_like(u, chunks=u.chunks)
        
        # Compute Gradients
        ux = sp().first_derivative(u, axis=0)
        uy = sp().first_derivative(u, axis=1)
        uz = sp().first_derivative(u, axis=2)
        vx = sp().first_derivative(v, axis=0)
        vy = sp().first_derivative(v, axis=1)
        vz = sp().first_derivative(v, axis=2)
        
        # Smooth Gradients
        ux = ux.map_blocks(ndi.uniform_filter, size=kernel, dtype=ux.dtype)
        uy = uy.map_blocks(ndi.uniform_filter, size=kernel, dtype=ux.dtype)
        uz = uz.map_blocks(ndi.uniform_filter, size=kernel, dtype=ux.dtype)
        vx = vx.map_blocks(ndi.uniform_filter, size=kernel, dtype=ux.dtype)
        vy = vy.map_blocks(ndi.uniform_filter, size=kernel, dtype=ux.dtype)
        vz = vz.map_blocks(ndi.uniform_filter, size=kernel, dtype=ux.dtype)
        
        u = util.trim_dask_array(u, kernel) 
        v = util.trim_dask_array(v, kernel) 
        w = util.trim_dask_array(w, kernel) 
        ux = util.trim_dask_array(ux, kernel) 
        uy = util.trim_dask_array(uy, kernel) 
        uz = util.trim_dask_array(uz, kernel)
        vx = util.trim_dask_array(vx, kernel)
        vy = util.trim_dask_array(vy, kernel)
        vz = util.trim_dask_array(vz, kernel)
        
        wx = da.zeros_like(ux, chunks=ux.chunks, dtype=ux.dtype)
        wy = da.zeros_like(ux, chunks=ux.chunks, dtype=ux.dtype)
        wz = da.zeros_like(ux, chunks=ux.chunks, dtype=ux.dtype)
        
        uv = u * v
        vw = v * w
        u2 = u * u
        v2 = v * v
        w2 = w * w
        denom = (u2 + v2 + w2) ** 1.5
        
        # First Fundamental Form Coefficients
        E = 1 + u2
        F = uv
        G = 1 + v2
        
        # Second Fundamental Form Coefficients
        e = (ux * v2 - uy * uv + uz * w2) / denom
        f = (vx * v2 - vy * uv + vz * w2) / denom
        g = (ux * vw - uy * u2 + (uz + vx) * uv - vy * u2 + vz * vw) / denom
        
        # Curvature Calculations
        H = (E * g - 2 * F * f + G * e) / (2 * (E * G - F * F))  # Mean Curvature
        K = (e * g - f * f) / (E * G - F * F)  # Gaussian Curvature
        
        # Principal Curvatures
        Kmax = H + da.sqrt(da.maximum(0, H * H - K))
        Kmin = H - da.sqrt(da.maximum(0, H * H - K))
        
        # Most Positive and Most Negative Curvatures
        KMPos = da.maximum(Kmax, Kmin)
        KMNeg = da.minimum(Kmax, Kmin)
        
        return (H, K, Kmax, Kmin, KMPos, KMNeg)