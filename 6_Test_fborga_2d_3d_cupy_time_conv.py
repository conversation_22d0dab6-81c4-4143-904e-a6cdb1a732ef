# test_fborga_2d_3d_optimized.py
"""
Performance and Validation Test for fborga_2d_3d_optimized.py
Compares:
  - Original NumPy/CPU fborga
  - CuPy-optimized (FFT-based)
Validates that outputs match to numerical tolerance.
"""
import numpy as np
import matplotlib.pyplot as plt
import time

from fborga_2d_3d import fborga_2d as fborga_2d_orig  # Original
from fborga_2d_3d_optimized_fixed import fborga_2d as fborga_2d_opt

# --- Synthetic Data for Testing ---
nt, ntr = 1024, 50
f1, f2 = 15, 55  # Hz
np.random.seed(42)
dt = 0.002  # 2 ms

t = np.arange(nt) * dt
section = (
    np.sin(2 * np.pi * f1 * t)[:, None] * np.ones((1, ntr))
    + 0.7 * np.sin(2 * np.pi * f2 * t)[:, None] * np.ones((1, ntr))
    + 0.1 * np.random.randn(nt, ntr)
)

# --- Parameters ---
fwidth = 10.0
finc = 2.0
target_freqs = [5, 10, 15, 20, 25, 30]  # Hz

print("\n== Borga Transform Test on Synthetic Data ==")
print(f"Section shape: {section.shape}, dt: {dt}")
print(f"Target frequencies: {target_freqs} Hz, fwidth: {fwidth}, finc: {finc}\n")

# --- Run Original Implementation (NumPy/CPU) ---
start = time.time()
tvs_ref, fout_ref, t_out_ref = fborga_2d_orig(
    section, t, fwidth, finc, target_freqs=target_freqs
)
elapsed_ref = time.time() - start
print(f"Original fborga_2d (CPU) time: {elapsed_ref:.3f} s\n")

# --- Run CuPy-Optimized Implementation (FFT) ---
tvs_cupy = None
tvs_cupy_np = None
try:
    import cupy  # test if cupy available
    start = time.time()
    tvs_cupy, fout_cupy, t_out_cupy = fborga_2d_opt(
        section, t, fwidth, finc, algorithm="fft", backend="cupy", target_freqs=target_freqs
    )
    tvs_cupy_np = cupy.asnumpy(tvs_cupy)  # Convert to NumPy array for plotting
    elapsed_cupy = time.time() - start
    print(f"CuPy-optimized fborga_2d (FFT, GPU) time: {elapsed_cupy:.3f} s\n")
except ImportError:
    print("CuPy not available, skipping GPU test.\n")

# --- Validation ---
def compare_arrays(arr1, arr2, desc, atol=1e-5, rtol=1e-3):
    max_abs = np.max(np.abs(arr1 - arr2))
    if np.allclose(arr1, arr2, atol=atol, rtol=rtol):
        print(f"{desc}: PASS  (max abs diff = {max_abs:.2e})")
    else:
        print(f"{desc}: FAIL  (max abs diff = {max_abs:.2e})")

if tvs_cupy_np is not None:
    compare_arrays(tvs_ref, tvs_cupy_np, "Original vs CuPy (FFT)")

# --- Plot a summary ---
voice_idx = 1  # which frequency to plot
plt.figure(figsize=(15, 5))
plt.subplot(131)
plt.imshow(tvs_ref[:, voice_idx, :], aspect='auto', cmap='seismic')
plt.title('Original fborga_2d')
plt.colorbar()

if tvs_cupy_np is not None:
    plt.subplot(132)
    plt.imshow(tvs_cupy_np[:, voice_idx, :], aspect='auto', cmap='seismic')
    plt.title('CuPy (FFT)')
    plt.colorbar()

    plt.subplot(133)
    plt.plot(tvs_ref[:, voice_idx, 0], label='Original')
    plt.plot(tvs_cupy_np[:, voice_idx, 0], ':', label='CuPy')
    plt.title('Voice Component Comparison (First Trace)')
    plt.legend()

plt.tight_layout()
plt.show()

print("\nAll tests complete.")
