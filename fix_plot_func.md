# --- Assume util.load_seismic_data is modified ---
# It should now return: seismic_data, metadata, trace_headers
# where trace_headers is a list of dictionaries.

# ... inside your main function after running fborga_2d ...

def plot_wiggle_trace_section(ax, data, t, x_coords, title, gain=1.0):
    """Plots a wiggle trace section."""
    ax.set_title(title)
    ax.set_xlabel('Spatial Coordinate (e.g., CDP Number or Distance)')
    ax.set_ylabel('Time (s)')
    
    # Calculate spacing for the plot
    trace_spacing = np.mean(np.diff(x_coords))
    
    for i, trace in enumerate(data.T): # Iterate through columns (traces)
        # Apply gain and scale the trace to fit within the trace spacing
        scaled_trace = trace * gain * trace_spacing
        
        # Plot the wiggle trace centered at its x-coordinate
        ax.plot(x_coords[i] + scaled_trace, t, 'k-', lw=0.5)
        # Fill positive wiggles
        ax.fill_betweenx(t, x_coords[i], x_coords[i] + scaled_trace, 
                         where=(scaled_trace > 0), color='k')
    
    ax.invert_yaxis()
    ax.set_xlim(x_coords.min() - trace_spacing, x_coords.max() + trace_spacing)

# --- In your visualization block for 2D data ---

# First, get the coordinates from the loaded headers
# Let's use CDP number as the spatial coordinate for this example.
# You would need to load this from your segyio trace headers.
# This assumes you have a `trace_headers` list from util.py
try:
    import segyio
    x_coordinates = np.array([h[segyio.TraceField.CDP] for h in metadata['trace_headers']])
except (KeyError, ImportError):
    # Fallback to simple trace number if headers are not available
    x_coordinates = np.arange(seismic_data_numpy.shape[0])


# ... create fig, axes ...

# Plot original seismic with wiggle traces
ax = axes[0, 0]
# The loaded data is (trace, time), so we pass it directly
plot_wiggle_trace_section(ax, seismic_data_numpy, time_axis, x_coordinates, 'Original Seismic (2D)')

# Plot spectral voices with wiggle traces
plot_idx = 1
for freq_idx in range(n_voices):
    row = plot_idx // n_cols
    col = plot_idx % n_cols
    ax = axes[row, col]
    
    # tvs_3d is (time, freq, trace). We need (trace, time) for our plotter
    voice_data = tvs_3d[:, freq_idx, :].T
    
    plot_wiggle_trace_section(ax, voice_data, t_out, x_coordinates, f'Spectral Voice\n{fout_selected[freq_idx]:.1f} Hz', gain=2.0)
    
    plot_idx += 1

# ... rest of plotting code ...