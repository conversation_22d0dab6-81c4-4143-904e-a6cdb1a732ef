# -*- coding: utf-8 -*-
"""
fborga_extended.py

Original script by dev<PERSON><PERSON>agustian<PERSON>, based on <PERSON><PERSON><PERSON><PERSON>'s Matlab code.
This version is extended by an AI assistant to handle 2D and 3D seismic data,
and to allow for the selection of specific output frequencies.
CORRECTED VERSION: Fixes the ImportError for 'ifftrl'.
ENHANCED VERSION: Adds SEG-Y file loading capability using proven approach from multispectral coherence script.
"""

import numpy as np
from scipy import signal as sig
import matplotlib.pyplot as plt
import os
import time

# SEG-Y loading and GUI imports
import util
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, ttk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# Import Borga transform functions from dedicated module
from fborga_2d_3d import fborga_2d, fborga_3d

# --- WIGGLE TRACE PLOTTING FUNCTION ---
def plot_wiggle_trace_section(ax, data, t, x_coords, title, gain=1.0):
    """Plots a wiggle trace section with proper dimension handling."""
    ax.set_title(title)
    ax.set_xlabel('Spatial Coordinate (e.g., CDP Number or Distance)')
    ax.set_ylabel('Time (s)')
    
    # Validate input dimensions
    print(f"DEBUG: plot_wiggle_trace_section - data shape: {data.shape}, t length: {len(t)}, x_coords length: {len(x_coords)}")
    
    # Ensure data is 2D
    if data.ndim != 2:
        raise ValueError(f"Data must be 2D, got shape {data.shape}")
    
    # Check if we need to transpose data to match (time, trace) format
    if data.shape[0] == len(x_coords) and data.shape[1] == len(t):
        # Data is (trace, time), transpose to (time, trace)
        data = data.T
        print("DEBUG: Transposed data from (trace, time) to (time, trace)")
    elif data.shape[0] == len(t) and data.shape[1] == len(x_coords):
        # Data is already (time, trace)
        print("DEBUG: Data is already in (time, trace) format")
    else:
        # Try to match the smaller dimension to time
        if data.shape[1] == len(t):
            data = data.T
            print("DEBUG: Transposed data based on time axis matching")
        elif data.shape[0] != len(t):
            print(f"WARNING: Time axis length ({len(t)}) doesn't match either data dimension {data.shape}")
            # Truncate or pad the time axis to match data
            if len(t) > data.shape[0]:
                t = t[:data.shape[0]]
                print(f"DEBUG: Truncated time axis to {len(t)} samples")
            elif len(t) < data.shape[0]:
                # Extend time axis
                dt = t[1] - t[0] if len(t) > 1 else 0.004  # Default dt
                t_extended = np.arange(data.shape[0]) * dt + t[0]
                t = t_extended
                print(f"DEBUG: Extended time axis to {len(t)} samples")
    
    # Ensure x_coords matches the number of traces
    if len(x_coords) != data.shape[1]:
        print(f"WARNING: x_coords length ({len(x_coords)}) doesn't match trace count ({data.shape[1]})")
        x_coords = np.arange(data.shape[1])
        print("DEBUG: Reset x_coords to simple trace numbers")
    
    # Calculate spacing for the plot
    if len(x_coords) > 1:
        trace_spacing = np.mean(np.diff(x_coords))
        if trace_spacing == 0:
            trace_spacing = 1.0
    else:
        trace_spacing = 1.0
    
    # Calculate appropriate gain
    data_max = np.max(np.abs(data))
    if data_max == 0:
        print("WARNING: Data is all zeros, skipping plot")
        ax.text(0.5, 0.5, "No Data", transform=ax.transAxes, ha='center', va='center')
        return
    
    auto_gain = trace_spacing * 0.8 / data_max
    effective_gain = gain * auto_gain
    
    print(f"DEBUG: trace_spacing: {trace_spacing:.3f}, data_max: {data_max:.6f}, effective_gain: {effective_gain:.6f}")
    
    # Plot each trace
    for i in range(data.shape[1]):
        trace = data[:, i]
        
        # Apply gain and scale the trace
        scaled_trace = trace * effective_gain
        
        # Plot the wiggle trace centered at its x-coordinate
        try:
            ax.plot(x_coords[i] + scaled_trace, t, 'k-', lw=0.5)
            # Fill positive wiggles
            ax.fill_betweenx(t, x_coords[i], x_coords[i] + scaled_trace, 
                             where=(scaled_trace > 0), color='k', alpha=0.7)
        except Exception as e:
            print(f"WARNING: Failed to plot trace {i}: {e}")
            continue
    
    ax.invert_yaxis()
    ax.set_xlim(x_coords.min() - trace_spacing, x_coords.max() + trace_spacing)
    ax.grid(True, alpha=0.3)

# --- SEG-Y LOADING AND USER INTERFACE FUNCTIONS ---

def get_user_inputs():
    """Prompts the user for input file, output file, and Borga processing parameters."""
    root = tk.Tk()
    root.withdraw() # We don't need the main window, only dialogs

    # --- File Selection ---
    input_segy_path = filedialog.askopenfilename(
        title="Select Input SEGY File",
        filetypes=[("SEGY files", "*.sgy *.segy"), ("NumPy files", "*.npy"), ("All files", "*.*")],
        parent=root
    )
    if not input_segy_path:
        messagebox.showerror("Error", "Input file selection cancelled. Exiting.", parent=root)
        root.destroy()
        return None

    # Make output file optional
    save_output = messagebox.askyesno("Save Output", "Do you want to save the Borga spectral voices to a file?", parent=root)
    output_path = None
    if save_output:
        output_path = filedialog.asksaveasfilename(
            title="Specify Output File Path (Optional)",
            defaultextension=".npy",
            filetypes=[("NumPy files", "*.npy"), ("All files", "*.*")],
            parent=root
        )
        if not output_path:  # User cancelled the save dialog
            save_output = False

    # --- Borga Decomposition Parameters ---
    default_target_freqs = "5, 10, 15, 20, 25, 30"
    default_fwidth = "10"
    default_finc = "2"

    while True:
        target_frequencies_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter target frequencies (Hz, comma-separated):\n(e.g., {default_target_freqs})",
            initialvalue=default_target_freqs,
            parent=root
        )
        if target_frequencies_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            target_frequencies = [float(f.strip()) for f in target_frequencies_str.split(',')]
            if not target_frequencies:
                raise ValueError("List cannot be empty.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for target frequencies: {e}\nPlease use comma-separated numbers (e.g., 5,10,15).", parent=root)

    while True:
        fwidth_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter Gaussian window width (fwidth, Hz):\n(e.g., {default_fwidth})",
            initialvalue=default_fwidth,
            parent=root
        )
        if fwidth_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            fwidth = float(fwidth_str)
            if fwidth <= 0:
                raise ValueError("fwidth must be positive.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for fwidth: {e}\nPlease enter a positive number (e.g., 5.0).", parent=root)

    while True:
        finc_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter frequency increment (finc, Hz):\n(e.g., {default_finc})",
            initialvalue=default_finc,
            parent=root
        )
        if finc_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            finc = float(finc_str)
            if finc <= 0:
                raise ValueError("finc must be positive.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for finc: {e}\nPlease enter a positive number (e.g., 1.0).", parent=root)

    root.destroy() # Clean up the hidden root window

    return {
        "input_segy_path": input_segy_path,
        "output_path": output_path,
        "save_output": save_output,
        "target_frequencies": target_frequencies,
        "fwidth": fwidth,
        "finc": finc
    }


def check_segy_geometry(input_segy_path):
    """
    Check SEG-Y file geometry and detect crooked line issues.
    
    Returns:
        dict: Geometry information including warnings about crooked lines
    """
    print(f"Analyzing SEG-Y geometry for: {input_segy_path}")
    
    try:
        import segyio
        
        with segyio.open(input_segy_path, 'r', ignore_geometry=True) as segy:
            # Get basic file info
            n_traces = segy.tracecount
            n_samples = len(segy.samples)
            dt = segy.bin[segyio.BinField.Interval] / 1000000.0  # Convert to seconds
            
            # Sample trace headers to check geometry
            sample_size = min(100, n_traces)  # Sample first 100 traces or all if fewer
            cdp_x = []
            cdp_y = []
            inline_nums = []
            xline_nums = []
            
            for i in range(0, n_traces, max(1, n_traces // sample_size)):
                trace = segy.trace[i]
                header = segy.header[i]
                
                # Try to get coordinates
                try:
                    x = header[segyio.TraceField.CDP_X] or header[segyio.TraceField.SourceX]
                    y = header[segyio.TraceField.CDP_Y] or header[segyio.TraceField.SourceY]
                    cdp_x.append(x)
                    cdp_y.append(y)
                except:
                    cdp_x.append(0)
                    cdp_y.append(0)
                
                # Try to get line numbers
                try:
                    inline = header[segyio.TraceField.INLINE_3D] or header[segyio.TraceField.CDP]
                    xline = header[segyio.TraceField.CROSSLINE_3D] or header[segyio.TraceField.TraceNumber]
                    inline_nums.append(inline)
                    xline_nums.append(xline)
                except:
                    inline_nums.append(i)
                    xline_nums.append(1)
            
            cdp_x = np.array(cdp_x)
            cdp_y = np.array(cdp_y)
            inline_nums = np.array(inline_nums)
            xline_nums = np.array(xline_nums)
            
            # Analyze geometry
            geometry_info = {
                'n_traces': n_traces,
                'n_samples': n_samples,
                'dt': dt,
                'is_crooked': False,
                'geometry_type': '2D',
                'warnings': []
            }
            
            # Check for 3D geometry
            unique_inlines = len(np.unique(inline_nums))
            unique_xlines = len(np.unique(xline_nums))
            
            if unique_inlines > 1 and unique_xlines > 1:
                geometry_info['geometry_type'] = '3D'
                
                # Check for regular 3D grid
                expected_traces = unique_inlines * unique_xlines
                if abs(n_traces - expected_traces) > 0.1 * expected_traces:
                    geometry_info['is_crooked'] = True
                    geometry_info['warnings'].append(
                        f"3D geometry appears irregular: {n_traces} traces vs expected {expected_traces}"
                    )
            
            # Check coordinate regularity for 2D lines
            if geometry_info['geometry_type'] == '2D':
                # Calculate distances between consecutive points
                if len(cdp_x) > 1:
                    dx = np.diff(cdp_x)
                    dy = np.diff(cdp_y)
                    distances = np.sqrt(dx**2 + dy**2)
                    
                    if len(distances) > 0 and np.std(distances) > 0.5 * np.mean(distances):
                        geometry_info['is_crooked'] = True
                        geometry_info['warnings'].append(
                            "2D line appears to have irregular trace spacing (possibly crooked)"
                        )
                        
                    # Check for significant direction changes
                    if len(dx) > 2:
                        angles = np.arctan2(dy, dx)
                        angle_changes = np.diff(angles)
                        angle_changes = np.abs(np.mod(angle_changes + np.pi, 2*np.pi) - np.pi)
                        
                        if np.max(angle_changes) > np.pi/6:  # More than 30 degrees
                            geometry_info['is_crooked'] = True
                            geometry_info['warnings'].append(
                                "2D line has significant direction changes (crooked line detected)"
                            )
            
            # Check for missing or zero coordinates
            zero_coords = np.sum((cdp_x == 0) & (cdp_y == 0))
            if zero_coords > 0.1 * len(cdp_x):
                geometry_info['warnings'].append(
                    f"Warning: {zero_coords}/{len(cdp_x)} sampled traces have zero coordinates"
                )
            
            return geometry_info
            
    except ImportError:
        return {
            'n_traces': None,
            'n_samples': None,
            'dt': None,
            'is_crooked': False,
            'geometry_type': 'unknown',
            'warnings': ['segyio not available - cannot analyze geometry']
        }
    except Exception as e:
        return {
            'n_traces': None,
            'n_samples': None,
            'dt': None,
            'is_crooked': False,
            'geometry_type': 'unknown',
            'warnings': [f'Error analyzing geometry: {str(e)}']
        }

def main():
    """
    Main workflow for applying Borga decomposition to SEG-Y files.
    """
    temp_root_for_dialogs = None  # Ensure variable is always defined

    # --- 1. GET USER INPUTS ---
    params = get_user_inputs()
    if not params:
        print("Process cancelled due to missing inputs or user cancellation.")
        return

    input_segy_path = params["input_segy_path"]
    output_path = params["output_path"]
    target_frequencies = params["target_frequencies"]
    fwidth = params["fwidth"]
    finc = params["finc"]

    print("--- Starting Borga Decomposition Workflow ---")

    # --- 1.5. ANALYZE SEGY GEOMETRY (NEW) ---
    if input_segy_path.lower().endswith(('.sgy', '.segy')):
        print("\n--- Analyzing SEG-Y Geometry ---")
        geometry_info = check_segy_geometry(input_segy_path)
        
        print(f"Detected geometry type: {geometry_info['geometry_type']}")
        print(f"Number of traces: {geometry_info.get('n_traces', 'unknown')}")
        print(f"Number of samples: {geometry_info.get('n_samples', 'unknown')}")
        print(f"Sample interval: {geometry_info.get('dt', 'unknown')} seconds")
        
        if geometry_info['is_crooked']:
            print("⚠️  WARNING: Crooked line geometry detected!")
            
        for warning in geometry_info['warnings']:
            print(f"⚠️  {warning}")
            
        if geometry_info['is_crooked'] or geometry_info['warnings']:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
            
            warning_msg = "Geometry Analysis Results:\n\n"
            if geometry_info['is_crooked']:
                warning_msg += "• Crooked line geometry detected\n"
            for warning in geometry_info['warnings']:
                warning_msg += f"• {warning}\n"
                
            warning_msg += "\nThis may affect the quality of Borga transform results.\n"
            warning_msg += "Crooked lines may require regularization or special handling.\n\n"
            warning_msg += "Do you want to continue processing?"
            
            continue_processing = messagebox.askyesno(
                "Geometry Warning", 
                warning_msg,
                parent=temp_root_for_dialogs
            )
            
            temp_root_for_dialogs.destroy()
            
            if not continue_processing:
                print("Processing cancelled due to geometry concerns.")
                return

    # --- 2. LOAD SEISMIC DATA using util.py ---
    print(f"\nLoading seismic data from: {input_segy_path}")
    try:
        # Modified to capture trace headers if available
        load_result = util.load_seismic_data(input_segy_path)
        
        # Handle different return formats from util.py
        if len(load_result) == 3:
            seismic_data_numpy, metadata, trace_headers = load_result
            metadata['trace_headers'] = trace_headers
        else:
            seismic_data_numpy, metadata = load_result
            metadata['trace_headers'] = None
            
        dt = metadata['dt']

        if metadata['type'] == 'numpy' and dt is None:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
            dt_str = simpledialog.askstring("Input Sample Interval",
                                            f"Input file '{os.path.basename(input_segy_path)}' is NumPy type and lacks sample interval.\nPlease enter sample interval (dt) in seconds (e.g., 0.002 or 0.004):",
                                            parent=temp_root_for_dialogs)
            if dt_str:
                try:
                    dt = float(dt_str)
                    metadata['dt'] = dt # Update metadata with user-provided dt
                except ValueError:
                    messagebox.showerror("Error", "Invalid dt value. Exiting.", parent=temp_root_for_dialogs)
                    if temp_root_for_dialogs:
                        try:
                            temp_root_for_dialogs.destroy()
                        except tk.TclError:
                            pass  # Already destroyed, ignore
                    return
            else: # User cancelled or entered nothing
                messagebox.showerror("Error", "Sample interval (dt) is required for .npy files for processing. Exiting.", parent=temp_root_for_dialogs)
                if temp_root_for_dialogs:
                    try:
                        temp_root_for_dialogs.destroy()
                    except tk.TclError:
                        pass  # Already destroyed, ignore
                return

        # Handle data format and geometry
        if seismic_data_numpy.ndim == 2 and metadata['geometry'] == '2D':
            print(f"Input data is 2D with shape: {seismic_data_numpy.shape}")
            geometry = '2D'
        elif seismic_data_numpy.ndim == 3 and metadata['geometry'] == '3D':
            print(f"Input data is 3D with shape: {seismic_data_numpy.shape}")
            geometry = '3D'
        elif seismic_data_numpy.ndim == 2 and metadata['geometry'] == '3D':
            # This might be a single inline/crossline from 3D data
            seismic_data_numpy = seismic_data_numpy.reshape((1, seismic_data_numpy.shape[0], seismic_data_numpy.shape[1]))
            print(f"Input data was 2D from 3D source. Reshaped from {metadata['shape']} to {seismic_data_numpy.shape} for 3D workflow.")
            geometry = '3D'
        else:
            err_msg = f"Loaded data has {seismic_data_numpy.ndim} dimensions ({seismic_data_numpy.shape}). The workflow expects 2D or 3D data."
            if not temp_root_for_dialogs:
                temp_root_for_dialogs = tk.Tk()
                temp_root_for_dialogs.withdraw()
            messagebox.showerror("Data Shape Error", err_msg, parent=temp_root_for_dialogs)
            if temp_root_for_dialogs:
                try:
                    temp_root_for_dialogs.destroy()
                except tk.TclError:
                    pass  # Already destroyed, ignore
            return

        print(f"Successfully loaded: type='{metadata['type']}', geometry='{geometry}', shape={seismic_data_numpy.shape}, dt={dt}s")

    except FileNotFoundError as e:
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("File Not Found", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except ValueError as e: # Covers unsupported file type
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("File Load Error", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except IOError as e: # Covers SEGY reading issues
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("SEGY Read Error", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except Exception as e: # Catch-all for other unexpected loading errors
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("Loading Error", f"An unexpected error occurred during loading: {e}", parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    finally:
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore

    # Integration with robust loader: Use time_axis from metadata if present (SEGY), else fallback to np.arange(...)*dt (NumPy)
    time_axis = metadata['time_axis'] if metadata.get('time_axis') is not None else np.arange(seismic_data_numpy.shape[-1]) * dt
    print(f"Data loaded. Shape: {seismic_data_numpy.shape}, dt: {dt:.4f}s")
    
    # ADD DEBUG INFORMATION
    print(f"DEBUG: Data statistics - min: {np.min(seismic_data_numpy):.6f}, max: {np.max(seismic_data_numpy):.6f}, mean: {np.mean(seismic_data_numpy):.6f}")
    print(f"DEBUG: Data contains NaN: {np.any(np.isnan(seismic_data_numpy))}, contains Inf: {np.any(np.isinf(seismic_data_numpy))}")
    print(f"DEBUG: Time axis - min: {np.min(time_axis):.4f}s, max: {np.max(time_axis):.4f}s, samples: {len(time_axis)}")
    print(f"DEBUG: Time axis matches last dimension: {len(time_axis) == seismic_data_numpy.shape[-1]}")
    
    # Extract spatial coordinates for wiggle plotting
    try:
        if metadata.get('trace_headers') is not None and input_segy_path.lower().endswith(('.sgy', '.segy')):
            import segyio
            # Try to get CDP numbers or coordinates from headers
            x_coordinates = []
            for header in metadata['trace_headers']:
                try:
                    # Try CDP number first, then trace number as fallback
                    cdp = header.get(segyio.TraceField.CDP, header.get(segyio.TraceField.TraceNumber, 0))
                    if cdp == 0:
                        cdp = header.get(segyio.TraceField.FieldRecord, 0)
                    x_coordinates.append(cdp)
                except:
                    x_coordinates.append(len(x_coordinates))
            
            x_coordinates = np.array(x_coordinates)
            if len(x_coordinates) != seismic_data_numpy.shape[0]:
                print(f"WARNING: Coordinate array length ({len(x_coordinates)}) doesn't match trace count ({seismic_data_numpy.shape[0]})")
                x_coordinates = np.arange(seismic_data_numpy.shape[0])
        else:
            # Fallback to simple trace numbers
            x_coordinates = np.arange(seismic_data_numpy.shape[0])
            
        print(f"DEBUG: Spatial coordinates range: {x_coordinates.min()} to {x_coordinates.max()}")
        
    except Exception as e:
        print(f"WARNING: Could not extract spatial coordinates: {e}")
        x_coordinates = np.arange(seismic_data_numpy.shape[0])
    
    # VALIDATE INPUT DATA
    if np.all(seismic_data_numpy == 0):
        print("ERROR: Input data is all zeros!")
        temp_root_for_dialogs = tk.Tk()
        temp_root_for_dialogs.withdraw()
        messagebox.showerror("Data Error", "Input seismic data appears to be all zeros. Please check your input file.", parent=temp_root_for_dialogs)
        temp_root_for_dialogs.destroy()
        return
        
    if np.any(np.isnan(seismic_data_numpy)) or np.any(np.isinf(seismic_data_numpy)):
        print("WARNING: Input data contains NaN or Inf values. Attempting to clean...")
        seismic_data_numpy = np.nan_to_num(seismic_data_numpy, nan=0.0, posinf=0.0, neginf=0.0)

    # --- 3. APPLY BORGA TRANSFORM ---
    print(f"Applying Borga transform for target frequencies: {target_frequencies} Hz...")
    start_time = time.time()

    if geometry == '2D':
        # For 2D data: util loads as (trace, time), fborga_2d expects (time, trace)
        seismic_data_transposed = seismic_data_numpy.T  # (time, trace)
        
        # ADD DEBUG INFORMATION
        print(f"DEBUG: 2D transposed data shape: {seismic_data_transposed.shape}")
        print(f"DEBUG: Sample trace statistics - min: {np.min(seismic_data_transposed[:, 0]):.6f}, max: {np.max(seismic_data_transposed[:, 0]):.6f}")

        # Apply 2D Borga transform
        tvs_3d, fout_selected, t_out = fborga_2d(
            seismic_section=seismic_data_transposed,
            t=time_axis,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_frequencies
        )
        
        print(f"2D Borga transform complete. Output shape: {tvs_3d.shape}")
        
        # ADD RESULT VALIDATION
        if tvs_3d.size == 0:
            print("ERROR: Borga transform returned empty array!")
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
            messagebox.showerror("Processing Error", "Borga transform returned empty results. Check input parameters.", parent=temp_root_for_dialogs)
            temp_root_for_dialogs.destroy()
            return
        
        print(f"DEBUG: Borga output statistics - min: {np.min(tvs_3d):.6f}, max: {np.max(tvs_3d):.6f}, mean: {np.mean(tvs_3d):.6f}")

    elif geometry == '3D':
        # For 3D data: util loads as (inline, xline, time), fborga_3d expects (time, inline, xline)
        seismic_data_transposed = np.transpose(seismic_data_numpy, (2, 0, 1))  # (time, inline, xline)
        
        # ADD DEBUG INFORMATION
        print(f"DEBUG: 3D transposed data shape: {seismic_data_transposed.shape}")
        print(f"DEBUG: Sample trace statistics - min: {np.min(seismic_data_transposed[:, 0, 0]):.6f}, max: {np.max(seismic_data_transposed[:, 0, 0]):.6f}")

        # Apply 3D Borga transform
        tvs_4d, fout_selected, t_out = fborga_3d(
            seismic_volume=seismic_data_transposed,
            t=time_axis,
            fwidth=fwidth,
            finc=finc,
            target_frequencies=target_frequencies
        )

        print(f"3D Borga transform complete. Output shape: {tvs_4d.shape}")
        
        # ADD RESULT VALIDATION
        if tvs_4d.size == 0:
            print("ERROR: Borga transform returned empty array!")
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
            messagebox.showerror("Processing Error", "Borga transform returned empty results. Check input parameters.", parent=temp_root_for_dialogs)
            temp_root_for_dialogs.destroy()
            return
            
        print(f"DEBUG: Borga output statistics - min: {np.min(tvs_4d):.6f}, max: {np.max(tvs_4d):.6f}, mean: {np.mean(tvs_4d):.6f}")

    end_time = time.time()
    print(f"Borga decomposition complete in {end_time - start_time:.2f} seconds.")
    print(f"Generated {len(fout_selected)} voices at frequencies: {np.round(fout_selected, 1)} Hz")
    
    # ADD FREQUENCY VALIDATION
    if len(fout_selected) == 0:
        print("ERROR: No frequencies were selected!")
        temp_root_for_dialogs = tk.Tk()
        temp_root_for_dialogs.withdraw()
        messagebox.showerror("Processing Error", "No frequencies were selected for output. Check your target frequencies and data parameters.", parent=temp_root_for_dialogs)
        temp_root_for_dialogs.destroy()
        return

    # --- 4. SAVE RESULTS (if requested) ---
    if params["save_output"] and output_path:
        print(f"Saving Borga spectral voices to: {output_path}")
        try:
            if geometry == '2D':
                # Save the 3D result (time, freq, trace)
                np.save(output_path, tvs_3d)
                # Also save metadata
                metadata_path = output_path.replace('.npy', '_metadata.npy')
                save_metadata = {
                    'frequencies': fout_selected,
                    'time_axis': t_out,
                    'fwidth': fwidth,
                    'finc': finc,
                    'target_frequencies': target_frequencies,
                    'geometry': geometry,
                    'original_shape': seismic_data_numpy.shape,
                    'dt': dt
                }
                np.save(metadata_path, save_metadata)
                print(f"Successfully saved 2D Borga results and metadata")

            elif geometry == '3D':
                # Save the 4D result (time, freq, inline, xline)
                np.save(output_path, tvs_4d)
                # Also save metadata
                metadata_path = output_path.replace('.npy', '_metadata.npy')
                save_metadata = {
                    'frequencies': fout_selected,
                    'time_axis': t_out,
                    'fwidth': fwidth,
                    'finc': finc,
                    'target_frequencies': target_frequencies,
                    'geometry': geometry,
                    'original_shape': seismic_data_numpy.shape,
                    'dt': dt
                }
                np.save(metadata_path, save_metadata)
                print(f"Successfully saved 3D Borga results and metadata")

        except Exception as e:
            save_dialog_root = tk.Tk()
            save_dialog_root.withdraw()
            save_err_msg = f"Error during saving to '{output_path}': {e}"
            print(save_err_msg)
            messagebox.showerror("Save Error", save_err_msg, parent=save_dialog_root)
            save_dialog_root.destroy()

    # --- 5. VISUALIZE RESULTS ---
    print("Displaying Borga spectral voices...")

    # Create a tkinter window for plotting
    plot_window = tk.Tk()
    plot_window.title("Seismic Data and Borga Spectral Voices - Wiggle Display")

    # Create a frame for the plots
    frame = ttk.Frame(plot_window)
    frame.pack(fill=tk.BOTH, expand=True)

    if geometry == '2D':
        # For 2D data, show original and spectral voices with wiggle traces
        n_voices = len(fout_selected)
        n_cols = min(4, n_voices + 1)  # +1 for original, max 4 columns
        n_rows = int(np.ceil((n_voices + 1) / n_cols))

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 6 * n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        if n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Plot original seismic with wiggle traces
        ax = axes[0, 0]
        print(f"DEBUG: Plotting original data - shape: {seismic_data_numpy.shape}, time_axis: {len(time_axis)}, x_coords: {len(x_coordinates)}")
        plot_wiggle_trace_section(ax, seismic_data_numpy, time_axis, x_coordinates, 'Original Seismic (2D)')

        # Plot spectral voices with wiggle traces
        plot_idx = 1
        for freq_idx in range(n_voices):
            row = plot_idx // n_cols
            col = plot_idx % n_cols

            if row < n_rows and col < n_cols:
                ax = axes[row, col]
                
                # tvs_3d is (time, freq, trace). We need (trace, time) for our plotter
                voice_data = tvs_3d[:, freq_idx, :].T
                
                # Use magnitude for complex data
                if np.iscomplexobj(voice_data):
                    voice_data = np.abs(voice_data)
                
                print(f"DEBUG: Plotting voice {freq_idx} - shape: {voice_data.shape}, t_out: {len(t_out)}, x_coords: {len(x_coordinates)}")
                plot_wiggle_trace_section(ax, voice_data, t_out, x_coordinates, 
                                        f'Spectral Voice\n{fout_selected[freq_idx]:.1f} Hz', gain=2.0)

            plot_idx += 1

        # Hide unused subplots
        total_plots = n_voices + 1
        for idx in range(total_plots, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if row < n_rows and col < n_cols:
                axes[row, col].set_visible(False)

    elif geometry == '3D':
        # For 3D data, show middle inline slice with wiggle traces
        mid_il = seismic_data_numpy.shape[0] // 2
        n_voices = len(fout_selected)
        n_cols = min(4, n_voices + 1)  # +1 for original, max 4 columns
        n_rows = int(np.ceil((n_voices + 1) / n_cols))

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 6 * n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        if n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Create crossline coordinates for 3D display
        xline_coordinates = np.arange(seismic_data_numpy.shape[1])

        # Plot original seismic (middle inline) with wiggle traces
        ax = axes[0, 0]
        original_slice = seismic_data_numpy[mid_il, :, :]  # (xline, time)
        print(f"DEBUG: 3D original slice - shape: {original_slice.shape}, time_axis: {len(time_axis)}, xline_coords: {len(xline_coordinates)}")
        plot_wiggle_trace_section(ax, original_slice, time_axis, xline_coordinates, 
                                f'Original Seismic (IL: {mid_il})')

        # Plot spectral voices
        plot_idx = 1
        for freq_idx in range(n_voices):
            row = plot_idx // n_cols
            col = plot_idx % n_cols

            if row < n_rows and col < n_cols:
                ax = axes[row, col]

                # Get spectral voice data (time, freq, inline, xline)
                # Extract middle inline: tvs_4d[:, freq_idx, mid_il, :] -> (time, xline)
                voice_slice = tvs_4d[:, freq_idx, mid_il, :]  # (time, xline)

                # Use magnitude for complex data
                if np.iscomplexobj(voice_slice):
                    voice_slice = np.abs(voice_slice)

                print(f"DEBUG: 3D voice slice {freq_idx} - shape: {voice_slice.shape}, t_out: {len(t_out)}, xline_coords: {len(xline_coordinates)}")
                plot_wiggle_trace_section(ax, voice_slice.T, t_out, xline_coordinates,
                                        f'Spectral Voice\n{fout_selected[freq_idx]:.1f} Hz', gain=2.0)

            plot_idx += 1

        # Hide unused subplots
        total_plots = n_voices + 1
        for idx in range(total_plots, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if row < n_rows and col < n_cols:
                axes[row, col].set_visible(False)

    plt.tight_layout()

    # Embed the figure in the tkinter window
    canvas = FigureCanvasTkAgg(fig, master=frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    # Add a close button
    close_button = ttk.Button(plot_window, text="Close", command=plot_window.destroy)
    close_button.pack(pady=10)

    # Center the window
    window_width = 1400
    window_height = 900
    screen_width = plot_window.winfo_screenwidth()
    screen_height = plot_window.winfo_screenheight()
    x = (screen_width // 2) - (window_width // 2)
    y = (screen_height // 2) - (window_height // 2)
    plot_window.geometry(f'{window_width}x{window_height}+{x}+{y}')

    print("--- Borga Decomposition Workflow Complete ---")

    # Start the tkinter main loop for the plot window
    plot_window.mainloop()


# --- DEMONSTRATION AND MAIN EXECUTION ---
def run_synthetic_demo():
    """
    Run a demonstration with synthetic data (original functionality).
    This function preserves the original demonstration capability.
    """
    print("\n--- Running Synthetic Data Demonstration ---")
    nt = 512
    dt = 0.002
    t = np.arange(0, nt * dt, dt)
    fwidth = 10.0
    finc = 2.0
    n_traces_2d = 50
    seismic_2d = np.zeros((nt, n_traces_2d))
    for i in range(n_traces_2d):
        trace = np.sin(2 * np.pi * 20 * t) * sig.windows.tukey(nt, 0.25)
        trace += 1.2 * np.sin(2 * np.pi * 55 * t) * sig.windows.tukey(nt, 0.25, nt // 2)
        trace += np.random.randn(nt) * 0.2
        seismic_2d[:, i] = trace

    target_frequencies = [20, 55, 80]
    tvs_3d_selected, fout_selected, t_out = fborga_2d(
        seismic_2d, t, fwidth, finc, target_freqs=target_frequencies
    )

    print("\n--- 2D Analysis with Frequency Selection ---")
    print(f"Input 2D section shape: {seismic_2d.shape}")
    print(f"Requested frequencies: {target_frequencies} Hz")
    print(f"Actual frequencies returned: {fout_selected} Hz")
    print(f"Output 3D Borga spectrum shape: {tvs_3d_selected.shape}")

    fig, axes = plt.subplots(1, 3, figsize=(15, 6), sharey=True)
    fig.suptitle('Borga Frequency Slices from Targeted Selection (Synthetic Data)', fontsize=16)

    for i in range(len(fout_selected)):
        ax = axes[i]
        freq_slice = tvs_3d_selected[:, i, :]
        im = ax.imshow(freq_slice, aspect='auto', cmap='seismic',
                       extent=[0, n_traces_2d, t_out[-1], t_out[0]],
                       vmin=-np.max(np.abs(freq_slice)), vmax=np.max(np.abs(freq_slice)))
        ax.set_title(f'Slice at {fout_selected[i]:.1f} Hz')
        ax.set_xlabel('Trace Number')
        fig.colorbar(im, ax=ax, orientation='horizontal', pad=0.15)

    axes[0].set_ylabel('Time (s)')
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.show()


if __name__ == '__main__':
    # Ask user whether to run SEG-Y workflow or synthetic demo
    root = tk.Tk()
    root.withdraw()

    choice = messagebox.askyesnocancel(
        "Borga Transform Workflow",
        "Choose your workflow:\n\n"
        "YES: Load and process SEG-Y files (recommended)\n"
        "NO: Run synthetic data demonstration\n"
        "CANCEL: Exit",
        parent=root
    )

    root.destroy()

    if choice is True:
        # Run the main SEG-Y workflow
        main()
    elif choice is False:
        # Run the synthetic data demonstration
        run_synthetic_demo()
    else:
        # User cancelled
        print("Workflow cancelled by user.")
        exit()